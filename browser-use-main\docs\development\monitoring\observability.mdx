---
title: "Observability"
description: "Trace Browser Use's agent execution steps and browser sessions"
icon: "eye"
mode: "wide"
---

## Overview

Browser Use has a native integration with [Laminar](https://lmnr.ai) - open-source platform for tracing, evals and labeling of AI agents.
Read more about Laminar in the [Laminar docs](https://docs.lmnr.ai).

## Setup



Register on [Laminar Cloud](https://lmnr.ai) and get the key from your project settings.
Set the `LMNR_PROJECT_API_KEY` environment variable.
```bash
pip install 'lmnr[all]'
export LMNR_PROJECT_API_KEY=<your-project-api-key>
```

## Usage

Then, you simply initialize the Laminar at the top of your project and both Browser Use and session recordings will be automatically traced.

```python {5-8}
from browser_use import Agent, ChatOpenAI
import asyncio

from lmnr import Laminar, Instruments
# this line auto-instruments Browser Use and any browser you use (local or remote)
Laminar.initialize(project_api_key="...", disabled_instruments={Instruments.BROWSER_USE})

async def main():
    agent = Agent(
        task="open google, search Laminar AI",
        llm=ChatOpenAI(model="gpt-4.1-mini"),
    )
    await agent.run()

asyncio.run(main())
```

## Viewing Traces

You can view traces in the Laminar UI by going to the traces tab in your project.
When you select a trace, you can see both the browser session recording and the agent execution steps.

Timeline of the browser session is synced with the agent execution steps, timeline highlights indicate the agent's current step synced with the browser session.
In the trace view, you can also see the agent's current step, the tool it's using, and the tool's input and output. Tools are highlighted in the timeline with a yellow color.

<img className="block" src="/images/laminar.png" alt="Laminar" />

## Laminar

To learn more about tracing and evaluating your browser agents, check out the [Laminar docs](https://docs.lmnr.ai).

## Browser Use Cloud Authentication

Browser Use can sync your agent runs to the cloud for easy viewing and sharing. Authentication is required to protect your data.

### Quick Setup

```bash
# Authenticate once to enable cloud sync for all future runs
browser-use auth
# Or if using module directly:
python -m browser_use.cli auth
```

**Note**: Cloud sync is enabled by default. If you've disabled it, you can re-enable with `export BROWSER_USE_CLOUD_SYNC=true`.

### Manual Authentication

```python
# Authenticate from code after task completion
from browser_use import Agent

agent = Agent(task="your task")
await agent.run()

# Later, authenticate for future runs
await agent.authenticate_cloud_sync()
```

### Reset Authentication

```bash
# Force re-authentication with a different account
rm ~/.config/browseruse/cloud_auth.json
browser-use auth
```

**Note**: Authentication uses OAuth Device Flow - you must complete the auth process while the command is running. Links expire when the polling stops.
