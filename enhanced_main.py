"""
Enhanced main entry point for the production browser automation system.
"""

import asyncio
import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.agents.enhanced_agent import <PERSON>hanced<PERSON><PERSON>, AgentSettings
from src.browser.enhanced_browser import EnhancedBrowserSession
from src.llm.enhanced_llm import EnhancedB<PERSON><PERSON><PERSON>rovider
from src.llm.llm_factory import create_llm
from src.tools.enhanced_tools import EnhancedBrowserTools
from src.events import default_event_bus
from src.utils.enhanced_config import ENHANCED_CONFIG
from src.examples.production_examples import ProductionExampleRunner

logger = logging.getLogger(__name__)


class BrowserAutomationCLI:
    """Command-line interface for the browser automation system."""
    
    def __init__(self):
        self.config = ENHANCED_CONFIG.load_config()
        self.setup_logging()
    
    def setup_logging(self):
        """Set up logging configuration."""
        log_level = logging.INFO
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Create logs directory
        Path("logs").mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler("logs/browser_automation.log")
            ]
        )
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """Create command-line argument parser."""
        parser = argparse.ArgumentParser(
            description="Enhanced Browser Automation System",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  python enhanced_main.py --task "Navigate to google.com and search for 'AI news'"
  python enhanced_main.py --examples --example web_scraping
  python enhanced_main.py --interactive
  python enhanced_main.py --config --show
            """
        )
        
        # Task execution
        parser.add_argument(
            "--task", "-t",
            type=str,
            help="Task description to execute"
        )
        
        parser.add_argument(
            "--max-steps",
            type=int,
            default=10,
            help="Maximum number of steps for task execution"
        )
        
        # Examples
        parser.add_argument(
            "--examples",
            action="store_true",
            help="Run production examples"
        )
        
        parser.add_argument(
            "--example",
            type=str,
            choices=["web_scraping", "form_automation", "ecommerce", "social_media", "research"],
            help="Run specific example"
        )
        
        # Interactive mode
        parser.add_argument(
            "--interactive", "-i",
            action="store_true",
            help="Run in interactive mode"
        )
        
        # Configuration
        parser.add_argument(
            "--config",
            action="store_true",
            help="Configuration management"
        )
        
        parser.add_argument(
            "--show",
            action="store_true",
            help="Show current configuration"
        )
        
        # Browser options
        parser.add_argument(
            "--headless",
            action="store_true",
            help="Run browser in headless mode"
        )
        
        parser.add_argument(
            "--debug",
            action="store_true",
            help="Enable debug logging"
        )
        
        return parser
    
    async def execute_task(self, task: str, max_steps: int = 10, headless: bool = False) -> Dict[str, Any]:
        """Execute a single task."""
        logger.info(f"Executing task: {task}")
        
        # Override headless setting if specified
        if headless:
            browser_config = self.config.get('browser_profile', {})
            browser_config['headless'] = True
        
        async with EnhancedBrowserSession() as browser_session:
            # Create agent settings
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=3,
                max_actions_per_step=4,
                use_thinking=True,
                step_timeout=180
            )
            
            async with EnhancedAgent(
                task=task,
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                try:
                    result = await agent.run(task, max_steps=max_steps)
                    
                    logger.info("Task completed successfully")
                    return {
                        "success": True,
                        "result": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"Task execution failed: {e}")
                    return {
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_examples(self, specific_example: Optional[str] = None) -> Dict[str, Any]:
        """Run production examples."""
        runner = ProductionExampleRunner()
        
        if specific_example:
            logger.info(f"Running specific example: {specific_example}")
            
            example_methods = {
                "web_scraping": runner.run_web_scraping_example,
                "form_automation": runner.run_form_automation_example,
                "ecommerce": runner.run_ecommerce_example,
                "social_media": runner.run_social_media_example,
                "research": runner.run_research_example
            }
            
            if specific_example in example_methods:
                result = await example_methods[specific_example]()
                runner.save_results({"results": {specific_example: result}})
                return result
            else:
                raise ValueError(f"Unknown example: {specific_example}")
        else:
            logger.info("Running all production examples")
            results = await runner.run_all_examples()
            runner.save_results(results)
            return results
    
    async def interactive_mode(self):
        """Run in interactive mode."""
        logger.info("Starting interactive mode")
        print("\n=== Enhanced Browser Automation System ===")
        print("Type 'help' for commands, 'quit' to exit")
        
        async with EnhancedBrowserSession() as browser_session:
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=3,
                max_actions_per_step=4,
                use_thinking=True
            )
            
            async with EnhancedAgent(
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                while True:
                    try:
                        user_input = input("\n> ").strip()
                        
                        if user_input.lower() in ['quit', 'exit', 'q']:
                            break
                        elif user_input.lower() == 'help':
                            self.show_interactive_help()
                        elif user_input.lower() == 'stats':
                            self.show_stats(agent, browser_session)
                        elif user_input.lower() == 'screenshot':
                            await self.take_screenshot(browser_session)
                        elif user_input.lower().startswith('config'):
                            self.show_config()
                        elif user_input:
                            # Execute as task
                            print(f"Executing: {user_input}")
                            result = await agent.run(user_input, max_steps=15)
                            print(f"Result: {result.get('success', False)}")
                            
                            if not result.get('success', False):
                                print(f"Error: {result.get('error', 'Unknown error')}")
                        
                    except KeyboardInterrupt:
                        print("\nUse 'quit' to exit")
                    except Exception as e:
                        print(f"Error: {e}")
        
        print("Interactive mode ended")
    
    def show_interactive_help(self):
        """Show help for interactive mode."""
        print("""
Available commands:
  help        - Show this help message
  stats       - Show agent and browser statistics
  screenshot  - Take a screenshot of current page
  config      - Show current configuration
  quit/exit/q - Exit interactive mode
  
  Or type any task description to execute it.
        """)
    
    def show_stats(self, agent: EnhancedAgent, browser_session: EnhancedBrowserSession):
        """Show current statistics."""
        agent_stats = agent.get_stats()
        browser_stats = browser_session.get_stats()
        
        print(f"\n=== Agent Statistics ===")
        print(f"Tasks completed: {agent_stats.get('tasks_completed', 0)}")
        print(f"Tasks failed: {agent_stats.get('tasks_failed', 0)}")
        print(f"Success rate: {agent_stats.get('success_rate', 0):.1%}")
        print(f"Total execution time: {agent_stats.get('total_execution_time', 0):.2f}s")
        
        print(f"\n=== Browser Statistics ===")
        print(f"Pages created: {browser_stats.get('pages_created', 0)}")
        print(f"Navigation count: {browser_stats.get('navigation_count', 0)}")
        print(f"Error count: {browser_stats.get('error_count', 0)}")
        print(f"Uptime: {browser_stats.get('uptime', 0):.2f}s")
    
    async def take_screenshot(self, browser_session: EnhancedBrowserSession):
        """Take a screenshot."""
        try:
            screenshot_path = await browser_session.take_screenshot()
            if screenshot_path:
                print(f"Screenshot saved: {screenshot_path}")
            else:
                print("Failed to take screenshot")
        except Exception as e:
            print(f"Screenshot error: {e}")
    
    def show_config(self):
        """Show current configuration."""
        print(f"\n=== Current Configuration ===")
        config_data = self.config
        print(json.dumps(config_data, indent=2, default=str))
    
    async def run(self, args):
        """Main run method."""
        try:
            if args.debug:
                logging.getLogger().setLevel(logging.DEBUG)
            
            if args.config and args.show:
                self.show_config()
                return
            
            if args.examples:
                results = await self.run_examples(args.example)
                
                if args.example:
                    print(f"\nExample '{args.example}' completed:")
                    print(f"Success: {results.get('success', False)}")
                    if results.get('execution_time'):
                        print(f"Time: {results['execution_time']:.2f}s")
                else:
                    stats = results["overall_stats"]
                    print(f"\nAll examples completed:")
                    print(f"Success rate: {stats['success_rate']:.1%}")
                    print(f"Total time: {stats['total_execution_time']:.2f}s")
                
                return
            
            if args.interactive:
                await self.interactive_mode()
                return
            
            if args.task:
                result = await self.execute_task(
                    args.task,
                    max_steps=args.max_steps,
                    headless=args.headless
                )
                
                print(f"\nTask execution completed:")
                print(f"Success: {result['success']}")
                
                if result['success']:
                    print("Task completed successfully!")
                else:
                    print(f"Error: {result.get('error', 'Unknown error')}")
                
                return
            
            # No specific command, show help
            parser = self.create_argument_parser()
            parser.print_help()
            
        except Exception as e:
            logger.error(f"CLI execution failed: {e}")
            raise


async def main():
    """Main entry point."""
    cli = BrowserAutomationCLI()
    parser = cli.create_argument_parser()
    args = parser.parse_args()
    
    await cli.run(args)


if __name__ == "__main__":
    asyncio.run(main())
