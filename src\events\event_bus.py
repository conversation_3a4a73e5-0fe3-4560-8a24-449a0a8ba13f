"""
Event bus system for browser automation.
Based on browser-use event patterns for production reliability.
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from collections import defaultdict
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union
from uuid import uuid4

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

T = TypeVar('T', bound='Event')


class Event(BaseModel, ABC):
    """Base event class for all events in the system."""
    
    event_id: str = Field(default_factory=lambda: str(uuid4()))
    event_type: str = Field(default="")
    timestamp: datetime = Field(default_factory=datetime.now)
    source: Optional[str] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.event_type:
            self.event_type = self.__class__.__name__
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a human-readable description of the event."""
        pass


class EventResult(BaseModel):
    """Result of an event execution."""
    
    success: bool = True
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    timestamp: datetime = Field(default_factory=datetime.now)


class EventHandler:
    """Wrapper for event handlers with metadata."""
    
    def __init__(
        self,
        handler: Callable,
        priority: int = 0,
        async_handler: bool = None,
        error_handler: Optional[Callable] = None
    ):
        self.handler = handler
        self.priority = priority
        self.async_handler = async_handler if async_handler is not None else asyncio.iscoroutinefunction(handler)
        self.error_handler = error_handler
        self.handler_id = str(uuid4())
        self.created_at = datetime.now()
        self.call_count = 0
        self.error_count = 0
    
    async def execute(self, event: Event) -> EventResult:
        """Execute the handler with the event."""
        start_time = time.time()
        
        try:
            self.call_count += 1
            
            if self.async_handler:
                result = await self.handler(event)
            else:
                result = self.handler(event)
            
            execution_time = time.time() - start_time
            
            return EventResult(
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.error_count += 1
            execution_time = time.time() - start_time
            
            logger.error(f"Event handler {self.handler_id} failed: {e}")
            
            # Try error handler if available
            if self.error_handler:
                try:
                    if asyncio.iscoroutinefunction(self.error_handler):
                        await self.error_handler(event, e)
                    else:
                        self.error_handler(event, e)
                except Exception as error_handler_error:
                    logger.error(f"Error handler also failed: {error_handler_error}")
            
            return EventResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )


class EventBus:
    """Event bus for managing event publishing and subscription."""
    
    def __init__(self, name: str = "default"):
        self.name = name
        self.handlers: Dict[Type[Event], List[EventHandler]] = defaultdict(list)
        self.global_handlers: List[EventHandler] = []
        self.event_history: List[Event] = []
        self.max_history_size = 1000
        self.stats = {
            "events_published": 0,
            "events_handled": 0,
            "handler_errors": 0,
            "created_at": datetime.now().isoformat()
        }
        self._lock = asyncio.Lock()
    
    def subscribe(
        self,
        event_type: Type[T],
        handler: Callable[[T], Any],
        priority: int = 0,
        error_handler: Optional[Callable] = None
    ) -> str:
        """Subscribe to events of a specific type."""
        event_handler = EventHandler(
            handler=handler,
            priority=priority,
            error_handler=error_handler
        )
        
        self.handlers[event_type].append(event_handler)
        
        # Sort handlers by priority (higher priority first)
        self.handlers[event_type].sort(key=lambda h: h.priority, reverse=True)
        
        logger.debug(f"Subscribed handler {event_handler.handler_id} to {event_type.__name__}")
        
        return event_handler.handler_id
    
    def subscribe_global(
        self,
        handler: Callable[[Event], Any],
        priority: int = 0,
        error_handler: Optional[Callable] = None
    ) -> str:
        """Subscribe to all events."""
        event_handler = EventHandler(
            handler=handler,
            priority=priority,
            error_handler=error_handler
        )
        
        self.global_handlers.append(event_handler)
        
        # Sort global handlers by priority
        self.global_handlers.sort(key=lambda h: h.priority, reverse=True)
        
        logger.debug(f"Subscribed global handler {event_handler.handler_id}")
        
        return event_handler.handler_id
    
    def unsubscribe(self, handler_id: str) -> bool:
        """Unsubscribe a handler by ID."""
        # Check specific event handlers
        for event_type, handlers in self.handlers.items():
            for i, handler in enumerate(handlers):
                if handler.handler_id == handler_id:
                    del handlers[i]
                    logger.debug(f"Unsubscribed handler {handler_id} from {event_type.__name__}")
                    return True
        
        # Check global handlers
        for i, handler in enumerate(self.global_handlers):
            if handler.handler_id == handler_id:
                del self.global_handlers[i]
                logger.debug(f"Unsubscribed global handler {handler_id}")
                return True
        
        return False
    
    async def publish(self, event: Event) -> List[EventResult]:
        """Publish an event to all subscribers."""
        async with self._lock:
            self.stats["events_published"] += 1
            
            # Add to history
            self.event_history.append(event)
            if len(self.event_history) > self.max_history_size:
                self.event_history.pop(0)
            
            logger.debug(f"Publishing event: {event.event_type} ({event.event_id})")
            
            results = []
            
            # Execute global handlers first
            for handler in self.global_handlers:
                try:
                    result = await handler.execute(event)
                    results.append(result)
                    self.stats["events_handled"] += 1
                    
                    if not result.success:
                        self.stats["handler_errors"] += 1
                        
                except Exception as e:
                    logger.error(f"Global handler execution failed: {e}")
                    self.stats["handler_errors"] += 1
                    results.append(EventResult(success=False, error=str(e)))
            
            # Execute specific event type handlers
            event_type = type(event)
            for handler in self.handlers.get(event_type, []):
                try:
                    result = await handler.execute(event)
                    results.append(result)
                    self.stats["events_handled"] += 1
                    
                    if not result.success:
                        self.stats["handler_errors"] += 1
                        
                except Exception as e:
                    logger.error(f"Event handler execution failed: {e}")
                    self.stats["handler_errors"] += 1
                    results.append(EventResult(success=False, error=str(e)))
            
            return results
    
    def get_handlers_info(self) -> Dict[str, Any]:
        """Get information about registered handlers."""
        info = {
            "global_handlers": len(self.global_handlers),
            "specific_handlers": {},
            "total_handlers": len(self.global_handlers)
        }
        
        for event_type, handlers in self.handlers.items():
            event_name = event_type.__name__
            info["specific_handlers"][event_name] = len(handlers)
            info["total_handlers"] += len(handlers)
        
        return info
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics."""
        stats = self.stats.copy()
        stats.update({
            "name": self.name,
            "handlers_info": self.get_handlers_info(),
            "history_size": len(self.event_history),
            "max_history_size": self.max_history_size
        })
        return stats
    
    def get_recent_events(self, limit: int = 10) -> List[Event]:
        """Get recent events from history."""
        return self.event_history[-limit:] if self.event_history else []
    
    def clear_history(self):
        """Clear event history."""
        self.event_history.clear()
        logger.debug("Event history cleared")
    
    async def wait_for_event(
        self,
        event_type: Type[T],
        timeout: Optional[float] = None,
        condition: Optional[Callable[[T], bool]] = None
    ) -> Optional[T]:
        """Wait for a specific event to occur."""
        event_received = asyncio.Event()
        received_event = None
        
        def handler(event: T):
            nonlocal received_event
            if condition is None or condition(event):
                received_event = event
                event_received.set()
        
        # Subscribe to the event
        handler_id = self.subscribe(event_type, handler)
        
        try:
            # Wait for the event
            await asyncio.wait_for(event_received.wait(), timeout=timeout)
            return received_event
            
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for event {event_type.__name__}")
            return None
            
        finally:
            # Unsubscribe the handler
            self.unsubscribe(handler_id)


# Global event bus instance
default_event_bus = EventBus("default")
