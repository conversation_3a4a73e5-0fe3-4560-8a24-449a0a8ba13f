"""
Logging configuration and utilities for the browser automation system.
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import structlog
from rich.console import Console
from rich.logging import RichHandler
import json
from datetime import datetime

from .config import config


class ScreenshotLogger:
    """Handles screenshot logging and management."""
    
    def __init__(self, screenshot_dir: str = None):
        self.screenshot_dir = Path(screenshot_dir or config.logging.screenshot_dir)
        self.screenshot_dir.mkdir(parents=True, exist_ok=True)
    
    def save_screenshot(self, screenshot_data: bytes, filename: str = None) -> str:
        """Save screenshot data to file."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"screenshot_{timestamp}.png"
        
        filepath = self.screenshot_dir / filename
        with open(filepath, "wb") as f:
            f.write(screenshot_data)
        
        return str(filepath)
    
    def cleanup_old_screenshots(self, max_age_days: int = 7):
        """Remove screenshots older than specified days."""
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60
        
        for screenshot_file in self.screenshot_dir.glob("*.png"):
            if current_time - screenshot_file.stat().st_mtime > max_age_seconds:
                screenshot_file.unlink()


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, "agent_id"):
            log_entry["agent_id"] = record.agent_id
        if hasattr(record, "task_id"):
            log_entry["task_id"] = record.task_id
        if hasattr(record, "browser_session"):
            log_entry["browser_session"] = record.browser_session
        if hasattr(record, "screenshot_path"):
            log_entry["screenshot_path"] = record.screenshot_path
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging():
    """Configure logging for the application."""
    
    # Create logs directory
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.logging.log_level.upper()))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler with Rich formatting
    console = Console()
    console_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True
    )
    console_handler.setLevel(logging.INFO)
    
    # File handler for all logs
    file_handler = logging.FileHandler(logs_dir / "browser_automation.log")
    file_handler.setLevel(logging.DEBUG)
    
    # Configure formatters based on log format setting
    if config.logging.log_format.lower() == "json":
        file_formatter = JSONFormatter()
    else:
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
    
    file_handler.setFormatter(file_formatter)
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if config.logging.log_format.lower() == "json" 
            else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a configured logger instance."""
    return structlog.get_logger(name)


class AgentLogger:
    """Specialized logger for agent operations."""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.logger = get_logger(f"agent.{agent_id}")
        self.screenshot_logger = ScreenshotLogger() if config.logging.enable_screenshots else None
    
    def info(self, message: str, **kwargs):
        """Log info message with agent context."""
        self.logger.info(message, agent_id=self.agent_id, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with agent context."""
        self.logger.debug(message, agent_id=self.agent_id, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with agent context."""
        self.logger.warning(message, agent_id=self.agent_id, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with agent context."""
        self.logger.error(message, agent_id=self.agent_id, **kwargs)
    
    def log_task_start(self, task: str, task_id: str = None):
        """Log the start of a task."""
        self.logger.info(
            "Task started",
            agent_id=self.agent_id,
            task=task,
            task_id=task_id,
            event="task_start"
        )
    
    def log_task_complete(self, task: str, task_id: str = None, duration: float = None):
        """Log the completion of a task."""
        self.logger.info(
            "Task completed",
            agent_id=self.agent_id,
            task=task,
            task_id=task_id,
            duration=duration,
            event="task_complete"
        )
    
    def log_browser_action(self, action: str, element: str = None, value: str = None):
        """Log a browser action."""
        self.logger.debug(
            "Browser action",
            agent_id=self.agent_id,
            action=action,
            element=element,
            value=value,
            event="browser_action"
        )
    
    def log_screenshot(self, screenshot_data: bytes, context: str = None) -> Optional[str]:
        """Log a screenshot with context."""
        if not self.screenshot_logger:
            return None
        
        screenshot_path = self.screenshot_logger.save_screenshot(screenshot_data)
        self.logger.info(
            "Screenshot captured",
            agent_id=self.agent_id,
            screenshot_path=screenshot_path,
            context=context,
            event="screenshot"
        )
        return screenshot_path
    
    def log_error_with_screenshot(self, error: Exception, screenshot_data: bytes = None):
        """Log an error with optional screenshot."""
        screenshot_path = None
        if screenshot_data and self.screenshot_logger:
            screenshot_path = self.screenshot_logger.save_screenshot(screenshot_data)
        
        self.logger.error(
            "Agent error occurred",
            agent_id=self.agent_id,
            error=str(error),
            error_type=type(error).__name__,
            screenshot_path=screenshot_path,
            event="error",
            exc_info=True
        )


# Initialize logging on module import
setup_logging()
