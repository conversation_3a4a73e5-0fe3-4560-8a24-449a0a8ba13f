"""
Production examples demonstrating real-world usage patterns and best practices.
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..agents.enhanced_agent import EnhancedAgent, AgentSettings
from ..browser.enhanced_browser import EnhancedBrowserSession
from ..tools.enhanced_tools import Enhanced<PERSON><PERSON>erTools
from ..llm.enhanced_llm import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessageManager
from ..events import default_event_bus
from ..events.agent_events import TaskStartEvent, TaskCompleteEvent
from ..utils.enhanced_config import ENHANCED_CONFIG

logger = logging.getLogger(__name__)


class ProductionExampleRunner:
    """Runner for production examples with comprehensive monitoring."""
    
    def __init__(self):
        self.config = ENHANCED_CONFIG.load_config()
        self.results: List[Dict[str, Any]] = []
        
        # Set up event monitoring
        self._setup_event_monitoring()
    
    def _setup_event_monitoring(self):
        """Set up event monitoring for examples."""
        
        async def on_task_start(event: TaskStartEvent):
            logger.info(f"Task started: {event.task_description}")
        
        async def on_task_complete(event: TaskCompleteEvent):
            logger.info(f"Task completed in {event.execution_time:.2f}s with {event.steps_taken} steps")
        
        default_event_bus.subscribe(TaskStartEvent, on_task_start)
        default_event_bus.subscribe(TaskCompleteEvent, on_task_complete)
    
    async def run_web_scraping_example(self) -> Dict[str, Any]:
        """Example: Web scraping with error handling and data extraction."""
        logger.info("Running web scraping example")
        
        async with EnhancedBrowserSession() as browser_session:
            # Create enhanced agent
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=3,
                max_actions_per_step=5,
                use_thinking=True
            )
            
            async with EnhancedAgent(
                task="Extract the latest news headlines from a news website",
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                # Define the scraping task
                task = """
                Navigate to https://news.ycombinator.com and extract the top 10 news headlines.
                For each headline, get:
                1. Title text
                2. URL link
                3. Points/score if available
                4. Number of comments
                
                Return the data in a structured format.
                """
                
                try:
                    result = await agent.run(task, max_steps=15)
                    
                    return {
                        "example": "web_scraping",
                        "success": result.get("success", False),
                        "data": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"Web scraping example failed: {e}")
                    return {
                        "example": "web_scraping",
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_form_automation_example(self) -> Dict[str, Any]:
        """Example: Form filling and submission with validation."""
        logger.info("Running form automation example")
        
        async with EnhancedBrowserSession() as browser_session:
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=2,
                max_actions_per_step=3,
                step_timeout=120
            )
            
            async with EnhancedAgent(
                task="Fill out and submit a contact form",
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                task = """
                Navigate to https://httpbin.org/forms/post and fill out the contact form with:
                - Customer name: "John Doe"
                - Telephone: "************"
                - Email: "<EMAIL>"
                - Size: "Large"
                - Topping: "cheese"
                - Delivery time: "now"
                - Comments: "Please deliver to the front door"
                
                Submit the form and verify the submission was successful.
                """
                
                try:
                    result = await agent.run(task, max_steps=12)
                    
                    return {
                        "example": "form_automation",
                        "success": result.get("success", False),
                        "data": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"Form automation example failed: {e}")
                    return {
                        "example": "form_automation",
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_ecommerce_example(self) -> Dict[str, Any]:
        """Example: E-commerce product search and comparison."""
        logger.info("Running e-commerce example")
        
        async with EnhancedBrowserSession() as browser_session:
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=3,
                max_actions_per_step=4,
                use_thinking=True,
                step_timeout=180
            )
            
            async with EnhancedAgent(
                task="Search and compare products on an e-commerce site",
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                task = """
                Navigate to https://books.toscrape.com/ and:
                1. Search for books in the "Travel" category
                2. Find the top 5 highest-rated travel books
                3. For each book, extract:
                   - Title
                   - Price
                   - Rating (stars)
                   - Availability status
                4. Compare the books and recommend the best value option
                
                Provide a summary with your recommendation and reasoning.
                """
                
                try:
                    result = await agent.run(task, max_steps=20)
                    
                    return {
                        "example": "ecommerce",
                        "success": result.get("success", False),
                        "data": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"E-commerce example failed: {e}")
                    return {
                        "example": "ecommerce",
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_social_media_example(self) -> Dict[str, Any]:
        """Example: Social media monitoring and content analysis."""
        logger.info("Running social media example")
        
        async with EnhancedBrowserSession() as browser_session:
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=2,
                max_actions_per_step=3,
                use_thinking=True
            )
            
            async with EnhancedAgent(
                task="Monitor social media for trending topics",
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                task = """
                Navigate to https://reddit.com/r/technology and:
                1. Identify the top 10 trending posts
                2. For each post, extract:
                   - Title
                   - Number of upvotes
                   - Number of comments
                   - Post age
                   - Brief summary of the topic
                3. Identify common themes or trending technologies
                4. Provide insights about what's currently popular in tech
                
                Summarize the findings with key trends and insights.
                """
                
                try:
                    result = await agent.run(task, max_steps=18)
                    
                    return {
                        "example": "social_media",
                        "success": result.get("success", False),
                        "data": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"Social media example failed: {e}")
                    return {
                        "example": "social_media",
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_research_example(self) -> Dict[str, Any]:
        """Example: Research task with multiple sources and synthesis."""
        logger.info("Running research example")
        
        async with EnhancedBrowserSession() as browser_session:
            agent_settings = AgentSettings(
                use_vision=True,
                max_failures=3,
                max_actions_per_step=5,
                use_thinking=True,
                step_timeout=240
            )
            
            async with EnhancedAgent(
                task="Research a topic across multiple sources",
                browser_session=browser_session,
                settings=agent_settings
            ) as agent:
                
                task = """
                Research the topic "Artificial Intelligence in Healthcare" by:
                1. Visiting Wikipedia to get a general overview
                2. Finding recent news articles about AI in healthcare
                3. Looking for academic or research perspectives
                4. Identifying key applications, benefits, and challenges
                5. Synthesizing the information into a comprehensive summary
                
                Provide a well-structured research summary with:
                - Overview of AI in healthcare
                - Key applications and use cases
                - Benefits and advantages
                - Challenges and concerns
                - Future outlook
                - Sources consulted
                """
                
                try:
                    result = await agent.run(task, max_steps=25)
                    
                    return {
                        "example": "research",
                        "success": result.get("success", False),
                        "data": result,
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
                    
                except Exception as e:
                    logger.error(f"Research example failed: {e}")
                    return {
                        "example": "research",
                        "success": False,
                        "error": str(e),
                        "agent_stats": agent.get_stats(),
                        "browser_stats": browser_session.get_stats()
                    }
    
    async def run_all_examples(self) -> Dict[str, Any]:
        """Run all production examples and collect results."""
        logger.info("Running all production examples")
        
        examples = [
            ("web_scraping", self.run_web_scraping_example),
            ("form_automation", self.run_form_automation_example),
            ("ecommerce", self.run_ecommerce_example),
            ("social_media", self.run_social_media_example),
            ("research", self.run_research_example)
        ]
        
        results = {}
        overall_stats = {
            "total_examples": len(examples),
            "successful_examples": 0,
            "failed_examples": 0,
            "total_execution_time": 0.0
        }
        
        for example_name, example_func in examples:
            logger.info(f"Running example: {example_name}")
            
            try:
                import time
                start_time = time.time()
                
                result = await example_func()
                
                execution_time = time.time() - start_time
                result["execution_time"] = execution_time
                
                results[example_name] = result
                overall_stats["total_execution_time"] += execution_time
                
                if result.get("success", False):
                    overall_stats["successful_examples"] += 1
                    logger.info(f"Example {example_name} completed successfully in {execution_time:.2f}s")
                else:
                    overall_stats["failed_examples"] += 1
                    logger.warning(f"Example {example_name} failed in {execution_time:.2f}s")
                
            except Exception as e:
                logger.error(f"Example {example_name} crashed: {e}")
                results[example_name] = {
                    "example": example_name,
                    "success": False,
                    "error": f"Crashed: {e}",
                    "execution_time": 0.0
                }
                overall_stats["failed_examples"] += 1
        
        # Calculate success rate
        overall_stats["success_rate"] = (
            overall_stats["successful_examples"] / overall_stats["total_examples"]
            if overall_stats["total_examples"] > 0 else 0
        )
        
        return {
            "overall_stats": overall_stats,
            "results": results,
            "event_bus_stats": default_event_bus.get_stats()
        }
    
    def save_results(self, results: Dict[str, Any], output_path: str = "logs/production_examples_results.json"):
        """Save results to a JSON file."""
        import json
        
        # Ensure directory exists
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Add timestamp
        import datetime
        results["timestamp"] = datetime.datetime.now().isoformat()
        results["config"] = self.config
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to {output_path}")


async def main():
    """Main function to run production examples."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create runner and execute examples
    runner = ProductionExampleRunner()
    
    try:
        results = await runner.run_all_examples()
        
        # Save results
        runner.save_results(results)
        
        # Print summary
        stats = results["overall_stats"]
        print(f"\n=== Production Examples Summary ===")
        print(f"Total examples: {stats['total_examples']}")
        print(f"Successful: {stats['successful_examples']}")
        print(f"Failed: {stats['failed_examples']}")
        print(f"Success rate: {stats['success_rate']:.1%}")
        print(f"Total execution time: {stats['total_execution_time']:.2f}s")
        
        # Print individual results
        print(f"\n=== Individual Results ===")
        for example_name, result in results["results"].items():
            status = "✓" if result.get("success", False) else "✗"
            time_taken = result.get("execution_time", 0)
            print(f"{status} {example_name}: {time_taken:.2f}s")
            
            if not result.get("success", False) and "error" in result:
                print(f"  Error: {result['error']}")
        
        return results
        
    except Exception as e:
        logger.error(f"Failed to run production examples: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
