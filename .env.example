# LLM API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Default LLM Provider (openai, gemini, anthropic)
DEFAULT_LLM_PROVIDER=gemini
DEFAULT_MODEL=gemini-2.0-flash-exp

# Browser Configuration
BROWSER_TYPE=chromium
HEADLESS=false
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080

# Stealth Mode Settings
ENABLE_STEALTH=true
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
ENABLE_SCREENSHOTS=true
SCREENSHOT_DIR=logs/screenshots

# Performance Settings
MAX_CONCURRENT_AGENTS=3
REQUEST_TIMEOUT=60
RETRY_ATTEMPTS=3
RETRY_DELAY=2

# MCP Integration
ENABLE_MCP=false
MCP_SERVER_URL=http://localhost:8000

# Development Settings
DEBUG=false
ENABLE_PROFILING=false
SAVE_BROWSER_SESSIONS=true
