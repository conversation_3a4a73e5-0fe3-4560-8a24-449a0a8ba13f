"""
Enhanced tools system with action registry and robust error handling.
Based on browser-use patterns for production reliability.
"""

import asyncio
import inspect
import logging
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union
from abc import ABC, abstractmethod
from pydantic import BaseModel, Field

from ..events import default_event_bus
from ..events.browser_events import (
    ClickEvent, TypeEvent, NavigationEvent, ScrollEvent, 
    ElementFoundEvent, ElementNotFoundEvent, ScreenshotEvent
)
from ..browser.enhanced_browser import EnhancedBrowserSession

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ActionResult(BaseModel):
    """Result of executing an action."""
    
    success: bool = Field(...)
    action_type: str = Field(...)
    result: Optional[Any] = Field(default=None)
    error: Optional[str] = Field(default=None)
    execution_time: float = Field(...)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: float = Field(default_factory=time.time)
    
    # Special flags for agent decision making
    is_done: bool = Field(default=False)
    requires_retry: bool = Field(default=False)
    extracted_content: Optional[str] = Field(default=None)


class ActionDefinition(BaseModel):
    """Definition of an available action."""
    
    name: str = Field(...)
    description: str = Field(...)
    parameters: Dict[str, Any] = Field(default_factory=dict)
    category: str = Field(default="general")
    requires_element: bool = Field(default=False)
    can_fail: bool = Field(default=True)
    estimated_time: float = Field(default=1.0)  # seconds
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for LLM consumption."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters,
            "category": self.category,
            "requires_element": self.requires_element,
            "estimated_time": self.estimated_time
        }


def action(
    name: str,
    description: str,
    category: str = "general",
    requires_element: bool = False,
    estimated_time: float = 1.0
):
    """Decorator to register an action method."""
    def decorator(func: Callable) -> Callable:
        # Get function signature for parameters
        sig = inspect.signature(func)
        parameters = {}
        
        for param_name, param in sig.parameters.items():
            if param_name in ['self', 'browser_session']:
                continue
            
            param_info = {
                "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                "required": param.default == inspect.Parameter.empty,
            }
            
            if param.default != inspect.Parameter.empty:
                param_info["default"] = param.default
            
            parameters[param_name] = param_info
        
        # Store action metadata
        func._action_definition = ActionDefinition(
            name=name,
            description=description,
            parameters=parameters,
            category=category,
            requires_element=requires_element,
            estimated_time=estimated_time
        )
        
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            start_time = time.time()
            action_type = name
            
            try:
                # Execute the action
                result = await func(self, *args, **kwargs)
                execution_time = time.time() - start_time
                
                # Handle different return types
                if isinstance(result, ActionResult):
                    result.execution_time = execution_time
                    return result
                elif isinstance(result, bool):
                    return ActionResult(
                        success=result,
                        action_type=action_type,
                        result=result,
                        execution_time=execution_time
                    )
                else:
                    return ActionResult(
                        success=True,
                        action_type=action_type,
                        result=result,
                        execution_time=execution_time
                    )
                    
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Action {action_type} failed: {e}")
                
                return ActionResult(
                    success=False,
                    action_type=action_type,
                    error=str(e),
                    execution_time=execution_time,
                    requires_retry=True
                )
        
        return wrapper
    return decorator


class ActionRegistry:
    """Registry for managing available actions."""
    
    def __init__(self):
        self.actions: Dict[str, ActionDefinition] = {}
        self.handlers: Dict[str, Callable] = {}
    
    def register_action(self, definition: ActionDefinition, handler: Callable):
        """Register an action with its handler."""
        self.actions[definition.name] = definition
        self.handlers[definition.name] = handler
        logger.debug(f"Registered action: {definition.name}")
    
    def get_action(self, name: str) -> Optional[ActionDefinition]:
        """Get action definition by name."""
        return self.actions.get(name)
    
    def get_handler(self, name: str) -> Optional[Callable]:
        """Get action handler by name."""
        return self.handlers.get(name)
    
    def get_all_actions(self) -> Dict[str, ActionDefinition]:
        """Get all registered actions."""
        return self.actions.copy()
    
    def get_actions_by_category(self, category: str) -> Dict[str, ActionDefinition]:
        """Get actions by category."""
        return {
            name: definition
            for name, definition in self.actions.items()
            if definition.category == category
        }
    
    def get_action_list(self) -> List[Dict[str, Any]]:
        """Get list of actions for LLM consumption."""
        return [definition.to_dict() for definition in self.actions.values()]


class EnhancedBrowserTools:
    """Enhanced browser tools with action registry and robust error handling."""
    
    def __init__(self, browser_session: EnhancedBrowserSession):
        self.browser_session = browser_session
        self.registry = ActionRegistry()
        self.stats = {
            "actions_executed": 0,
            "actions_succeeded": 0,
            "actions_failed": 0,
            "total_execution_time": 0.0,
            "created_at": time.time()
        }
        
        # Register all action methods
        self._register_actions()
    
    def _register_actions(self):
        """Register all action methods with the registry."""
        for method_name in dir(self):
            method = getattr(self, method_name)
            if hasattr(method, '_action_definition'):
                definition = method._action_definition
                self.registry.register_action(definition, method)
    
    async def execute_action(self, action_name: str, **kwargs) -> ActionResult:
        """Execute an action by name."""
        handler = self.registry.get_handler(action_name)
        if not handler:
            return ActionResult(
                success=False,
                action_type=action_name,
                error=f"Unknown action: {action_name}",
                execution_time=0.0
            )
        
        # Update stats
        self.stats["actions_executed"] += 1
        
        try:
            result = await handler(**kwargs)
            
            if result.success:
                self.stats["actions_succeeded"] += 1
            else:
                self.stats["actions_failed"] += 1
            
            self.stats["total_execution_time"] += result.execution_time
            
            return result
            
        except Exception as e:
            self.stats["actions_failed"] += 1
            logger.error(f"Action execution failed: {e}")
            
            return ActionResult(
                success=False,
                action_type=action_name,
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="navigate_to",
        description="Navigate to a specific URL",
        category="navigation",
        estimated_time=3.0
    )
    async def navigate_to(self, url: str, wait_until: str = "domcontentloaded") -> ActionResult:
        """Navigate to a URL."""
        try:
            success = await self.browser_session.navigate_to(url, wait_until=wait_until)
            
            if success:
                page = await self.browser_session.get_current_page()
                current_url = page.url if page else url
                
                return ActionResult(
                    success=True,
                    action_type="navigate_to",
                    result={"url": current_url},
                    metadata={"target_url": url, "actual_url": current_url},
                    execution_time=0.0  # Will be set by decorator
                )
            else:
                return ActionResult(
                    success=False,
                    action_type="navigate_to",
                    error="Navigation failed",
                    execution_time=0.0
                )
                
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="navigate_to",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="click_element",
        description="Click on an element using CSS selector",
        category="interaction",
        requires_element=True,
        estimated_time=1.0
    )
    async def click_element(self, selector: str, timeout: float = 10.0) -> ActionResult:
        """Click on an element."""
        page = await self.browser_session.get_current_page()
        if not page:
            return ActionResult(
                success=False,
                action_type="click_element",
                error="No active page",
                execution_time=0.0
            )
        
        try:
            # Wait for element to be visible
            element = await page.wait_for_selector(selector, timeout=timeout * 1000)
            
            if not element:
                await default_event_bus.publish(ElementNotFoundEvent(
                    browser_session_id=self.browser_session.session_id,
                    page_url=page.url,
                    selector=selector,
                    timeout=timeout
                ))
                
                return ActionResult(
                    success=False,
                    action_type="click_element",
                    error=f"Element not found: {selector}",
                    execution_time=0.0
                )
            
            # Get element info
            element_text = await element.text_content()
            element_tag = await element.evaluate("el => el.tagName.toLowerCase()")
            
            # Click the element
            await element.click()
            
            # Publish click event
            await default_event_bus.publish(ClickEvent(
                browser_session_id=self.browser_session.session_id,
                page_url=page.url,
                selector=selector,
                element_text=element_text,
                element_tag=element_tag,
                success=True
            ))
            
            return ActionResult(
                success=True,
                action_type="click_element",
                result={"clicked": True, "element_text": element_text},
                metadata={"selector": selector, "element_tag": element_tag},
                execution_time=0.0
            )
            
        except Exception as e:
            await default_event_bus.publish(ClickEvent(
                browser_session_id=self.browser_session.session_id,
                page_url=page.url,
                selector=selector,
                success=False,
                error=str(e)
            ))
            
            return ActionResult(
                success=False,
                action_type="click_element",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="type_text",
        description="Type text into an input element",
        category="interaction",
        requires_element=True,
        estimated_time=2.0
    )
    async def type_text(
        self,
        selector: str,
        text: str,
        clear_first: bool = True,
        timeout: float = 10.0
    ) -> ActionResult:
        """Type text into an element."""
        page = await self.browser_session.get_current_page()
        if not page:
            return ActionResult(
                success=False,
                action_type="type_text",
                error="No active page",
                execution_time=0.0
            )
        
        try:
            # Wait for element
            element = await page.wait_for_selector(selector, timeout=timeout * 1000)
            
            if not element:
                return ActionResult(
                    success=False,
                    action_type="type_text",
                    error=f"Element not found: {selector}",
                    execution_time=0.0
                )
            
            # Clear if requested
            if clear_first:
                await element.clear()
            
            # Type the text
            await element.type(text)
            
            # Publish type event
            await default_event_bus.publish(TypeEvent(
                browser_session_id=self.browser_session.session_id,
                page_url=page.url,
                selector=selector,
                text=text,
                clear_first=clear_first,
                success=True
            ))
            
            return ActionResult(
                success=True,
                action_type="type_text",
                result={"typed": text},
                metadata={"selector": selector, "clear_first": clear_first},
                execution_time=0.0
            )
            
        except Exception as e:
            await default_event_bus.publish(TypeEvent(
                browser_session_id=self.browser_session.session_id,
                page_url=page.url,
                selector=selector,
                text=text,
                clear_first=clear_first,
                success=False,
                error=str(e)
            ))
            
            return ActionResult(
                success=False,
                action_type="type_text",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="scroll_page",
        description="Scroll the page in a specified direction",
        category="navigation",
        estimated_time=0.5
    )
    async def scroll_page(
        self,
        direction: str = "down",
        amount: int = 500
    ) -> ActionResult:
        """Scroll the page."""
        page = await self.browser_session.get_current_page()
        if not page:
            return ActionResult(
                success=False,
                action_type="scroll_page",
                error="No active page",
                execution_time=0.0
            )
        
        try:
            if direction == "down":
                await page.evaluate(f"window.scrollBy(0, {amount})")
            elif direction == "up":
                await page.evaluate(f"window.scrollBy(0, -{amount})")
            elif direction == "top":
                await page.evaluate("window.scrollTo(0, 0)")
            elif direction == "bottom":
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            else:
                return ActionResult(
                    success=False,
                    action_type="scroll_page",
                    error=f"Invalid direction: {direction}",
                    execution_time=0.0
                )
            
            # Publish scroll event
            await default_event_bus.publish(ScrollEvent(
                browser_session_id=self.browser_session.session_id,
                page_url=page.url,
                direction=direction,
                amount=amount,
                success=True
            ))
            
            return ActionResult(
                success=True,
                action_type="scroll_page",
                result={"scrolled": direction, "amount": amount},
                execution_time=0.0
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="scroll_page",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="take_screenshot",
        description="Take a screenshot of the current page",
        category="utility",
        estimated_time=2.0
    )
    async def take_screenshot(self, full_page: bool = True) -> ActionResult:
        """Take a screenshot."""
        try:
            screenshot_path = await self.browser_session.take_screenshot(full_page=full_page)
            
            if screenshot_path:
                # Publish screenshot event
                await default_event_bus.publish(ScreenshotEvent(
                    browser_session_id=self.browser_session.session_id,
                    screenshot_path=screenshot_path,
                    full_page=full_page,
                    success=True
                ))
                
                return ActionResult(
                    success=True,
                    action_type="take_screenshot",
                    result={"screenshot_path": screenshot_path},
                    metadata={"full_page": full_page},
                    execution_time=0.0
                )
            else:
                return ActionResult(
                    success=False,
                    action_type="take_screenshot",
                    error="Failed to take screenshot",
                    execution_time=0.0
                )
                
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="take_screenshot",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="get_page_content",
        description="Get the text content of the current page",
        category="extraction",
        estimated_time=1.0
    )
    async def get_page_content(self, selector: Optional[str] = None) -> ActionResult:
        """Get page content."""
        page = await self.browser_session.get_current_page()
        if not page:
            return ActionResult(
                success=False,
                action_type="get_page_content",
                error="No active page",
                execution_time=0.0
            )
        
        try:
            if selector:
                # Get content from specific element
                element = await page.query_selector(selector)
                if element:
                    content = await element.text_content()
                else:
                    return ActionResult(
                        success=False,
                        action_type="get_page_content",
                        error=f"Element not found: {selector}",
                        execution_time=0.0
                    )
            else:
                # Get full page content
                content = await page.evaluate("document.body.innerText")
            
            return ActionResult(
                success=True,
                action_type="get_page_content",
                result={"content": content},
                extracted_content=content,
                metadata={"selector": selector, "content_length": len(content)},
                execution_time=0.0
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="get_page_content",
                error=str(e),
                execution_time=0.0
            )
    
    @action(
        name="wait_for_element",
        description="Wait for an element to appear on the page",
        category="utility",
        requires_element=True,
        estimated_time=5.0
    )
    async def wait_for_element(self, selector: str, timeout: float = 10.0) -> ActionResult:
        """Wait for an element to appear."""
        page = await self.browser_session.get_current_page()
        if not page:
            return ActionResult(
                success=False,
                action_type="wait_for_element",
                error="No active page",
                execution_time=0.0
            )
        
        try:
            element = await page.wait_for_selector(selector, timeout=timeout * 1000)
            
            if element:
                # Get element info
                element_text = await element.text_content()
                element_tag = await element.evaluate("el => el.tagName.toLowerCase()")
                
                await default_event_bus.publish(ElementFoundEvent(
                    browser_session_id=self.browser_session.session_id,
                    page_url=page.url,
                    selector=selector,
                    element_count=1,
                    wait_time=0.0,  # Will be calculated by decorator
                    element_info={"text": element_text, "tag": element_tag}
                ))
                
                return ActionResult(
                    success=True,
                    action_type="wait_for_element",
                    result={"found": True, "element_text": element_text},
                    metadata={"selector": selector, "element_tag": element_tag},
                    execution_time=0.0
                )
            else:
                await default_event_bus.publish(ElementNotFoundEvent(
                    browser_session_id=self.browser_session.session_id,
                    page_url=page.url,
                    selector=selector,
                    timeout=timeout
                ))
                
                return ActionResult(
                    success=False,
                    action_type="wait_for_element",
                    error=f"Element not found within {timeout}s: {selector}",
                    execution_time=0.0
                )
                
        except Exception as e:
            return ActionResult(
                success=False,
                action_type="wait_for_element",
                error=str(e),
                execution_time=0.0
            )
    
    def get_available_actions(self) -> List[Dict[str, Any]]:
        """Get list of available actions for LLM consumption."""
        return self.registry.get_action_list()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get tools statistics."""
        stats = self.stats.copy()
        uptime = time.time() - self.stats["created_at"]
        
        stats.update({
            "uptime": uptime,
            "available_actions": len(self.registry.actions),
            "success_rate": (
                self.stats["actions_succeeded"] / self.stats["actions_executed"]
                if self.stats["actions_executed"] > 0 else 0
            ),
            "average_execution_time": (
                self.stats["total_execution_time"] / self.stats["actions_executed"]
                if self.stats["actions_executed"] > 0 else 0
            )
        })
        
        return stats
