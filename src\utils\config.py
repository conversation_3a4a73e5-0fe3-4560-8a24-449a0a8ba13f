"""
Configuration management for the browser automation system.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class BrowserConfig(BaseSettings):
    """Browser-specific configuration settings."""
    
    browser_type: str = Field(default="chromium", description="Browser type to use")
    headless: bool = Field(default=False, description="Run browser in headless mode")
    timeout: int = Field(default=30000, description="Browser timeout in milliseconds")
    viewport_width: int = Field(default=1920, description="Browser viewport width")
    viewport_height: int = Field(default=1080, description="Browser viewport height")
    enable_stealth: bool = Field(default=True, description="Enable stealth mode")
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        description="User agent string"
    )
    
    class Config:
        env_prefix = "BROWSER_"


class LLMConfig(BaseSettings):
    """LLM provider configuration settings."""
    
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    gemini_api_key: Optional[str] = Field(default=None, description="Google Gemini API key")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    
    default_provider: str = Field(default="gemini", description="Default LLM provider")
    default_model: str = Field(default="gemini-2.0-flash-exp", description="Default model name")
    
    class Config:
        env_prefix = ""


class LoggingConfig(BaseSettings):
    """Logging configuration settings."""
    
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    enable_screenshots: bool = Field(default=True, description="Enable screenshot logging")
    screenshot_dir: str = Field(default="logs/screenshots", description="Screenshot directory")
    
    class Config:
        env_prefix = "LOG_"


class PerformanceConfig(BaseSettings):
    """Performance and concurrency settings."""
    
    max_concurrent_agents: int = Field(default=3, description="Maximum concurrent agents")
    request_timeout: int = Field(default=60, description="Request timeout in seconds")
    retry_attempts: int = Field(default=3, description="Number of retry attempts")
    retry_delay: int = Field(default=2, description="Delay between retries in seconds")
    
    class Config:
        env_prefix = ""


class MCPConfig(BaseSettings):
    """Model Context Protocol configuration."""
    
    enable_mcp: bool = Field(default=False, description="Enable MCP integration")
    mcp_server_url: str = Field(default="http://localhost:8000", description="MCP server URL")
    
    class Config:
        env_prefix = "MCP_"


class DevelopmentConfig(BaseSettings):
    """Development and debugging settings."""
    
    debug: bool = Field(default=False, description="Enable debug mode")
    enable_profiling: bool = Field(default=False, description="Enable performance profiling")
    save_browser_sessions: bool = Field(default=True, description="Save browser sessions")
    
    class Config:
        env_prefix = ""


class AppConfig:
    """Main application configuration class."""
    
    def __init__(self):
        self.browser = BrowserConfig()
        self.llm = LLMConfig()
        self.logging = LoggingConfig()
        self.performance = PerformanceConfig()
        self.mcp = MCPConfig()
        self.development = DevelopmentConfig()
        
        # Ensure required directories exist
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            "logs",
            "logs/screenshots",
            "config",
            "tests/fixtures"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_llm_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """Get LLM configuration for a specific provider."""
        provider = provider or self.llm.default_provider
        
        config = {
            "provider": provider,
            "model": self.llm.default_model
        }
        
        if provider == "openai":
            config["api_key"] = self.llm.openai_api_key
        elif provider == "gemini":
            config["api_key"] = self.llm.gemini_api_key
        elif provider == "anthropic":
            config["api_key"] = self.llm.anthropic_api_key
        
        return config
    
    def get_browser_config(self) -> Dict[str, Any]:
        """Get browser configuration dictionary."""
        return {
            "browser_type": self.browser.browser_type,
            "headless": self.browser.headless,
            "timeout": self.browser.timeout,
            "viewport": {
                "width": self.browser.viewport_width,
                "height": self.browser.viewport_height
            },
            "stealth": self.browser.enable_stealth,
            "user_agent": self.browser.user_agent
        }
    
    def validate_configuration(self) -> bool:
        """Validate that required configuration is present."""
        # Check that at least one LLM API key is configured
        api_keys = [
            self.llm.openai_api_key,
            self.llm.gemini_api_key,
            self.llm.anthropic_api_key
        ]
        
        if not any(api_keys):
            raise ValueError("At least one LLM API key must be configured")
        
        # Check that the default provider has an API key
        provider_key_map = {
            "openai": self.llm.openai_api_key,
            "gemini": self.llm.gemini_api_key,
            "anthropic": self.llm.anthropic_api_key
        }
        
        default_key = provider_key_map.get(self.llm.default_provider)
        if not default_key:
            raise ValueError(f"API key for default provider '{self.llm.default_provider}' is not configured")
        
        return True


# Global configuration instance
config = AppConfig()
