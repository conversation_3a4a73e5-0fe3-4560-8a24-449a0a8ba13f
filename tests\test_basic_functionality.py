"""
Basic functionality tests for the browser automation system.
"""

import pytest
import asyncio
import os
from pathlib import Path
import sys

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.agents import WebAgent
from src.browser import <PERSON><PERSON><PERSON><PERSON>anager
from src.utils import config
from src.llm import create_llm


class TestBasicFunctionality:
    """Test basic system functionality."""
    
    @pytest.fixture
    def event_loop(self):
        """Create an event loop for async tests."""
        loop = asyncio.new_event_loop()
        yield loop
        loop.close()
    
    @pytest.mark.asyncio
    async def test_browser_manager_initialization(self):
        """Test browser manager initialization."""
        browser_manager = BrowserManager()
        
        # Test initialization
        success = await browser_manager.initialize()
        assert success, "Browser manager should initialize successfully"
        assert browser_manager.is_initialized, "Browser manager should be marked as initialized"
        
        # Test page creation
        page = await browser_manager.get_page()
        assert page is not None, "Should be able to get a page"
        
        # Cleanup
        await browser_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_llm_provider_creation(self):
        """Test LLM provider creation."""
        # Skip if no API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available")
        
        llm = create_llm(provider="gemini", model="gemini-pro")
        assert llm is not None, "Should be able to create LLM provider"
        
        # Test basic response generation
        response = await llm.generate_response("Hello, how are you?")
        assert response is not None, "Should get a response from LLM"
        assert len(response) > 0, "Response should not be empty"
    
    @pytest.mark.asyncio
    async def test_web_agent_initialization(self):
        """Test web agent initialization."""
        # Skip if no API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available")
        
        agent = WebAgent(
            llm_provider="gemini",
            llm_model="gemini-pro"
        )
        
        # Test initialization
        success = await agent.initialize()
        assert success, "Agent should initialize successfully"
        assert agent.is_initialized, "Agent should be marked as initialized"
        
        # Cleanup
        await agent.cleanup()
    
    @pytest.mark.asyncio
    async def test_basic_navigation(self):
        """Test basic navigation functionality."""
        # Skip if no API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available")
        
        async with WebAgent(
            llm_provider="gemini",
            llm_model="gemini-pro"
        ) as agent:
            
            # Test navigation
            result = await agent.run("Navigate to https://httpbin.org")
            
            assert result is not None, "Should get a result from navigation"
            assert isinstance(result, dict), "Result should be a dictionary"
            
            # Check if navigation was successful
            page_info = await agent.get_page_info()
            assert "httpbin.org" in page_info.get("url", ""), "Should be on httpbin.org"
    
    @pytest.mark.asyncio
    async def test_screenshot_functionality(self):
        """Test screenshot functionality."""
        browser_manager = BrowserManager()
        await browser_manager.initialize()
        
        try:
            # Navigate to a page
            await browser_manager.navigate("https://httpbin.org")
            
            # Take screenshot
            page = await browser_manager.get_page()
            screenshot_path = await browser_manager.take_screenshot(page)
            
            assert screenshot_path is not None, "Should get a screenshot path"
            assert Path(screenshot_path).exists(), "Screenshot file should exist"
            
            # Cleanup screenshot
            Path(screenshot_path).unlink(missing_ok=True)
            
        finally:
            await browser_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_session_management(self):
        """Test session management functionality."""
        browser_manager = BrowserManager()
        await browser_manager.initialize()
        
        try:
            from src.browser import SessionManager
            session_manager = SessionManager()
            
            # Create a session
            success = await session_manager.create_session(
                "test_session",
                browser_manager.context,
                {"test": "data"}
            )
            assert success, "Should be able to create a session"
            
            # Save the session
            success = await session_manager.save_session("test_session")
            assert success, "Should be able to save a session"
            
            # List sessions
            sessions = session_manager.list_sessions()
            assert len(sessions) > 0, "Should have at least one session"
            
            # Get session info
            info = session_manager.get_session_info("test_session")
            assert info is not None, "Should get session info"
            assert info["session_id"] == "test_session", "Session ID should match"
            
            # Cleanup
            await session_manager.delete_session("test_session")
            
        finally:
            await browser_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in the system."""
        # Skip if no API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available")
        
        async with WebAgent(
            llm_provider="gemini",
            llm_model="gemini-pro"
        ) as agent:
            
            # Test navigation to invalid URL
            try:
                result = await agent.run("Navigate to https://this-site-does-not-exist-12345.com")
                # Should either handle gracefully or raise an exception
                if result:
                    assert "success" in result, "Result should indicate success status"
            except Exception as e:
                # Exception is acceptable for invalid URLs
                assert isinstance(e, Exception), "Should raise an exception for invalid URLs"
    
    def test_configuration_loading(self):
        """Test configuration loading."""
        # Test that config is loaded
        assert config is not None, "Config should be loaded"
        assert hasattr(config, 'browser'), "Config should have browser settings"
        assert hasattr(config, 'llm'), "Config should have LLM settings"
        assert hasattr(config, 'logging'), "Config should have logging settings"
    
    @pytest.mark.asyncio
    async def test_tools_initialization(self):
        """Test tools initialization."""
        browser_manager = BrowserManager()
        await browser_manager.initialize()
        
        try:
            from src.tools import BrowserTools
            tools = BrowserTools(browser_manager)
            
            success = await tools.initialize()
            assert success, "Tools should initialize successfully"
            assert tools.is_initialized, "Tools should be marked as initialized"
            
            # Test getting available tools
            available_tools = tools.get_available_tools()
            assert len(available_tools) > 0, "Should have available tools"
            assert "click_element" in available_tools, "Should have click_element tool"
            assert "type_text" in available_tools, "Should have type_text tool"
            
        finally:
            await browser_manager.cleanup()


class TestIntegrationScenarios:
    """Test integration scenarios."""
    
    @pytest.fixture
    def event_loop(self):
        """Create an event loop for async tests."""
        loop = asyncio.new_event_loop()
        yield loop
        loop.close()
    
    @pytest.mark.asyncio
    async def test_complete_workflow(self):
        """Test a complete automation workflow."""
        # Skip if no API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            pytest.skip("GOOGLE_API_KEY not available")
        
        async with WebAgent(
            llm_provider="gemini",
            llm_model="gemini-pro"
        ) as agent:
            
            # Step 1: Navigate
            result1 = await agent.run("Navigate to https://httpbin.org")
            assert result1 is not None, "Navigation should succeed"
            
            # Step 2: Take screenshot
            screenshot_path = await agent.browser_manager.take_screenshot()
            assert screenshot_path is not None, "Screenshot should be taken"
            
            # Step 3: Get page info
            page_info = await agent.get_page_info()
            assert "httpbin.org" in page_info.get("url", ""), "Should be on correct page"
            
            # Step 4: Check agent stats
            stats = agent.get_stats()
            assert stats["tasks_completed"] >= 1, "Should have completed at least one task"
            
            # Cleanup screenshot
            if screenshot_path and Path(screenshot_path).exists():
                Path(screenshot_path).unlink()


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
