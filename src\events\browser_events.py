"""
Browser-specific events for the automation system.
"""

from typing import Any, Dict, Optional
from pydantic import Field

from .event_bus import Event


class BrowserEvent(Event):
    """Base class for browser-related events."""
    
    browser_session_id: Optional[str] = Field(default=None)
    page_url: Optional[str] = Field(default=None)
    page_title: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Browser event on {self.page_url or 'unknown page'}"


class NavigationEvent(BrowserEvent):
    """Event fired when navigating to a new URL."""
    
    target_url: str = Field(...)
    navigation_type: str = Field(default="navigate")  # navigate, back, forward, reload
    wait_until: str = Field(default="domcontentloaded")
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Navigation to {self.target_url}"


class ClickEvent(BrowserEvent):
    """Event fired when clicking an element."""
    
    selector: str = Field(...)
    element_text: Optional[str] = Field(default=None)
    element_tag: Optional[str] = Field(default=None)
    coordinates: Optional[Dict[str, float]] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Click on element {self.selector}"


class TypeEvent(BrowserEvent):
    """Event fired when typing text into an element."""
    
    selector: str = Field(...)
    text: str = Field(...)
    clear_first: bool = Field(default=True)
    typing_speed: Optional[float] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Type '{self.text[:50]}...' into {self.selector}"


class ScrollEvent(BrowserEvent):
    """Event fired when scrolling the page."""
    
    direction: str = Field(default="down")  # down, up, left, right, top, bottom
    amount: int = Field(default=500)
    element_selector: Optional[str] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        if self.element_selector:
            return f"Scroll {self.direction} on element {self.element_selector}"
        return f"Scroll {self.direction} by {self.amount}px"


class PageLoadEvent(BrowserEvent):
    """Event fired when a page finishes loading."""
    
    load_time: float = Field(...)
    load_state: str = Field(default="domcontentloaded")
    resource_count: Optional[int] = Field(default=None)
    error_count: Optional[int] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Page loaded in {self.load_time:.2f}s"


class ErrorEvent(BrowserEvent):
    """Event fired when a browser error occurs."""
    
    error_type: str = Field(...)
    error_message: str = Field(...)
    stack_trace: Optional[str] = Field(default=None)
    recoverable: bool = Field(default=False)
    retry_count: int = Field(default=0)
    
    def get_description(self) -> str:
        return f"Browser error: {self.error_type} - {self.error_message}"


class ElementFoundEvent(BrowserEvent):
    """Event fired when an element is found."""
    
    selector: str = Field(...)
    element_count: int = Field(default=1)
    wait_time: float = Field(default=0.0)
    element_info: Optional[Dict[str, Any]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Found {self.element_count} element(s) matching {self.selector}"


class ElementNotFoundEvent(BrowserEvent):
    """Event fired when an element is not found."""
    
    selector: str = Field(...)
    timeout: float = Field(...)
    retry_count: int = Field(default=0)
    
    def get_description(self) -> str:
        return f"Element not found: {self.selector} (timeout: {self.timeout}s)"


class ScreenshotEvent(BrowserEvent):
    """Event fired when a screenshot is taken."""
    
    screenshot_path: str = Field(...)
    full_page: bool = Field(default=True)
    file_size: Optional[int] = Field(default=None)
    success: bool = Field(default=True)
    
    def get_description(self) -> str:
        return f"Screenshot saved to {self.screenshot_path}"


class FileUploadEvent(BrowserEvent):
    """Event fired when uploading a file."""
    
    selector: str = Field(...)
    file_path: str = Field(...)
    file_size: Optional[int] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Upload file {self.file_path} to {self.selector}"


class FormSubmitEvent(BrowserEvent):
    """Event fired when submitting a form."""
    
    form_selector: str = Field(...)
    submit_method: str = Field(default="click")  # click, submit, enter
    form_data: Optional[Dict[str, Any]] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Submit form {self.form_selector}"


class TabSwitchEvent(BrowserEvent):
    """Event fired when switching browser tabs."""
    
    from_tab_id: Optional[str] = Field(default=None)
    to_tab_id: str = Field(...)
    tab_count: int = Field(default=1)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Switch to tab {self.to_tab_id}"


class TabCloseEvent(BrowserEvent):
    """Event fired when closing a browser tab."""
    
    tab_id: str = Field(...)
    remaining_tabs: int = Field(default=0)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Close tab {self.tab_id}"


class CookieEvent(BrowserEvent):
    """Event fired when managing cookies."""
    
    action: str = Field(...)  # get, set, delete, clear
    cookie_name: Optional[str] = Field(default=None)
    cookie_value: Optional[str] = Field(default=None)
    domain: Optional[str] = Field(default=None)
    cookie_count: Optional[int] = Field(default=None)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        if self.cookie_name:
            return f"Cookie {self.action}: {self.cookie_name}"
        return f"Cookie {self.action} operation"


class JavaScriptExecutionEvent(BrowserEvent):
    """Event fired when executing JavaScript."""
    
    script: str = Field(...)
    result: Optional[Any] = Field(default=None)
    execution_time: float = Field(default=0.0)
    success: Optional[bool] = Field(default=None)
    error: Optional[str] = Field(default=None)
    
    def get_description(self) -> str:
        script_preview = self.script[:100] + "..." if len(self.script) > 100 else self.script
        return f"Execute JavaScript: {script_preview}"


class NetworkRequestEvent(BrowserEvent):
    """Event fired for network requests."""
    
    url: str = Field(...)
    method: str = Field(default="GET")
    status_code: Optional[int] = Field(default=None)
    response_time: Optional[float] = Field(default=None)
    request_headers: Optional[Dict[str, str]] = Field(default=None)
    response_headers: Optional[Dict[str, str]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Network request: {self.method} {self.url}"


class PerformanceEvent(BrowserEvent):
    """Event fired for performance metrics."""
    
    metric_name: str = Field(...)
    metric_value: float = Field(...)
    metric_unit: str = Field(default="ms")
    additional_metrics: Optional[Dict[str, float]] = Field(default=None)
    
    def get_description(self) -> str:
        return f"Performance metric: {self.metric_name} = {self.metric_value}{self.metric_unit}"
