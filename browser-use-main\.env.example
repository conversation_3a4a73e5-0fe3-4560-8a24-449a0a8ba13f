# Browser Use Configuration
# Copy this file to .env and fill in your values

# Logging Configuration
# Set the logging level (debug, info, warning, error)
BROWSER_USE_LOGGING_LEVEL=info

# Log file paths (optional)
# Save debug level logs to this file
BROWSER_USE_DEBUG_LOG_FILE=debug.log

# Save info level logs to this file  
BROWSER_USE_INFO_LOG_FILE=info.log

# CDP (Chrome DevTools Protocol) logging level
CDP_LOGGING_LEVEL=WARNING

# Telemetry and Analytics
# Enable/disable anonymous telemetry
ANONYMIZED_TELEMETRY=true

# Browser Use Cloud Configuration (optional)
# Your Browser Use Cloud API key - get it from: https://cloud.browser-use.com/billing
# BROWSER_USE_API_KEY=your_api_key_here

# Custom API base URL (for enterprise installations)
# BROWSER_USE_CLOUD_API_URL=https://api.browser-use.com

# Cloud sync settings
# BROWSER_USE_CLOUD_SYNC=false

# Model Configuration
# Default LLM model to use
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# AZURE_OPENAI_API_KEY=
# AZURE_OPENAI_ENDPOINT=
# GOOGLE_API_KEY=
# DEEPSEEK_API_KEY=
# GROK_API_KEY=
# NOVITA_API_KEY=

# Browser Configuration  
# Path to Chrome/Chromium executable (optional)
# BROWSER_USE_EXECUTABLE_PATH=/path/to/chrome

# Run browser in headless mode
# BROWSER_USE_HEADLESS=false

# User data directory for browser profile
# BROWSER_USE_USER_DATA_DIR=./browser_data

# Proxy Configuration (optional)
# BROWSER_USE_PROXY_SERVER=http://proxy.example.com:8080
# BROWSER_USE_NO_PROXY=localhost,127.0.0.1,*.internal
# BROWSER_USE_PROXY_USERNAME=username
# BROWSER_USE_PROXY_PASSWORD=password
