"""
Enhanced agent implementation based on browser-use patterns.
Features proper state management, event-driven architecture, and robust error handling.
"""

import asyncio
import gc
import json
import logging
import time
import traceback
from collections.abc import Awaitable, Callable
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Generic, TypeVar
from uuid import uuid4

from pydantic import BaseModel, Field, ConfigDict
from uuid_extensions import uuid7str

from ..utils import get_logger, AgentLogger, ErrorHandler, TaskExecutionError
from ..utils.enhanced_config import ENHANCED_CONFIG
from ..llm import create_llm, BaseLLMProvider
from ..browser import BrowserManager, SessionManager
from ..tools import BrowserTools

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)


class AgentSettings(BaseModel):
    """Configuration options for the Enhanced Agent."""
    
    use_vision: bool = True
    vision_detail_level: str = 'auto'  # 'auto', 'low', 'high'
    save_conversation_path: Optional[Union[str, Path]] = None
    max_failures: int = 3
    max_actions_per_step: int = 4
    use_thinking: bool = True
    flash_mode: bool = False
    max_history_items: Optional[int] = None
    calculate_cost: bool = False
    llm_timeout: int = 60
    step_timeout: int = 180
    final_response_after_failure: bool = True


class AgentState(BaseModel):
    """Holds all state information for an Enhanced Agent."""
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    agent_id: str = Field(default_factory=uuid7str)
    n_steps: int = 1
    consecutive_failures: int = 0
    last_result: Optional[List[Dict[str, Any]]] = None
    last_plan: Optional[str] = None
    last_model_output: Optional[Dict[str, Any]] = None
    
    # Pause/resume state
    paused: bool = False
    stopped: bool = False
    session_initialized: bool = False
    follow_up_task: bool = False
    
    # Execution context
    current_task: Optional[str] = None
    task_history: List[Dict[str, Any]] = Field(default_factory=list)
    execution_context: Dict[str, Any] = Field(default_factory=dict)
    
    # Performance tracking
    stats: Dict[str, Any] = Field(default_factory=lambda: {
        "tasks_completed": 0,
        "tasks_failed": 0,
        "total_execution_time": 0.0,
        "created_at": datetime.now().isoformat()
    })


class AgentStepInfo:
    """Information about the current step."""
    
    def __init__(self, step_number: int, max_steps: int):
        self.step_number = step_number
        self.max_steps = max_steps
    
    def is_last_step(self) -> bool:
        """Check if this is the last step."""
        return self.step_number >= self.max_steps - 1


class ActionResult(BaseModel):
    """Result of executing an action."""
    
    # For done action
    is_done: bool = False
    success: Optional[bool] = None
    
    # Error handling
    error: Optional[str] = None
    
    # Files and attachments
    attachments: Optional[List[str]] = None
    
    # Always include in long term memory
    extracted_content: Optional[str] = None
    include_extracted_content_only_once: bool = False
    
    # Additional metadata
    action_type: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class EnhancedAgent:
    """Enhanced agent with production-ready patterns from browser-use."""
    
    def __init__(
        self,
        task: str = None,
        llm: Optional[BaseLLMProvider] = None,
        browser_session: Optional[BrowserManager] = None,
        settings: Optional[AgentSettings] = None,
        agent_id: Optional[str] = None,
        **kwargs
    ):
        # Initialize core components
        self.state = AgentState(agent_id=agent_id or uuid7str())
        self.settings = settings or AgentSettings()
        self.logger = AgentLogger(self.state.agent_id)
        self.error_handler = ErrorHandler(self.state.agent_id)
        
        # Task and LLM setup
        self.task = task
        self.llm = llm or self._create_default_llm()
        
        # Browser management
        self.browser_session = browser_session or BrowserManager()
        self.session_manager = SessionManager()
        
        # Tools
        self.tools: Optional[BrowserTools] = None
        
        # Event callbacks
        self.task_callbacks: Dict[str, List[Callable]] = {}
        self.error_callbacks: List[Callable] = []
        
        # Initialization flag
        self.is_initialized = False
        
        # Update state with initial task
        if task:
            self.state.current_task = task
    
    def _create_default_llm(self) -> BaseLLMProvider:
        """Create default LLM from configuration."""
        config = ENHANCED_CONFIG.load_config()
        llm_config = config.get('llm', {})
        
        provider = llm_config.get('provider', 'gemini')
        model = llm_config.get('model', 'gemini-2.0-flash-exp')
        
        return create_llm(provider=provider, model=model)
    
    async def initialize(self) -> bool:
        """Initialize the enhanced agent."""
        try:
            self.logger.info("Initializing enhanced agent")
            
            # Initialize browser session
            if not self.browser_session.is_initialized:
                await self.browser_session.initialize()
            
            # Initialize tools
            self.tools = BrowserTools(self.browser_session, self.logger)
            await self.tools.initialize()
            
            # Create default session
            await self.session_manager.create_session(
                session_id="default",
                context=self.browser_session.context,
                metadata={"agent_id": self.state.agent_id}
            )
            
            self.is_initialized = True
            self.state.session_initialized = True
            
            self.logger.info("Enhanced agent initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize enhanced agent: {e}")
            self.error_handler.handle_task_error(e, "agent_initialization")
            return False
    
    async def run(self, task: str = None, max_steps: int = 10) -> Dict[str, Any]:
        """Main entry point for running tasks with enhanced error handling."""
        if not self.is_initialized:
            await self.initialize()
        
        # Use provided task or stored task
        current_task = task or self.task
        if not current_task:
            raise ValueError("No task provided")
        
        self.state.current_task = current_task
        task_id = str(uuid4())
        start_time = datetime.now()
        
        try:
            self.logger.log_task_start(current_task, task_id)
            
            # Execute the task with step-by-step processing
            result = await self._execute_task_with_steps(current_task, max_steps)
            
            # Calculate execution time
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Update state
            task_record = {
                "id": task_id,
                "description": current_task,
                "started_at": start_time.isoformat(),
                "completed_at": end_time.isoformat(),
                "execution_time": execution_time,
                "result": result,
                "success": result.get("success", False)
            }
            
            self.state.task_history.append(task_record)
            self.state.stats["tasks_completed"] += 1
            self.state.stats["total_execution_time"] += execution_time
            
            # Log completion
            self.logger.log_task_complete(current_task, task_id, execution_time)
            
            # Call completion callbacks
            await self._call_task_callbacks("completed", task_record)
            
            return result
            
        except Exception as e:
            # Handle error
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            error_info = self.error_handler.handle_task_error(e, current_task)
            
            # Update state
            task_record = {
                "id": task_id,
                "description": current_task,
                "started_at": start_time.isoformat(),
                "failed_at": end_time.isoformat(),
                "execution_time": execution_time,
                "error": str(e),
                "error_info": error_info,
                "success": False
            }
            
            self.state.task_history.append(task_record)
            self.state.stats["tasks_failed"] += 1
            self.state.stats["total_execution_time"] += execution_time
            self.state.consecutive_failures += 1
            
            # Take screenshot for debugging
            screenshot_data = await self._take_error_screenshot()
            if screenshot_data:
                self.logger.log_error_with_screenshot(e, screenshot_data)
            
            # Call error callbacks
            await self._call_error_callbacks(e, task_record)
            
            # Check if we should attempt final recovery
            if (self.settings.final_response_after_failure and 
                self.state.consecutive_failures >= self.settings.max_failures):
                
                try:
                    recovery_result = await self._attempt_final_recovery(current_task, str(e))
                    if recovery_result.get("success"):
                        return recovery_result
                except Exception as recovery_error:
                    self.logger.error(f"Final recovery attempt failed: {recovery_error}")
            
            # Re-raise if not recoverable
            if not error_info.get("recoverable", False):
                raise TaskExecutionError(f"Task failed: {e}") from e
            
            return {
                "success": False,
                "error": str(e),
                "error_info": error_info,
                "task_id": task_id
            }
    
    async def _execute_task_with_steps(self, task: str, max_steps: int) -> Dict[str, Any]:
        """Execute task with step-by-step processing."""
        self.logger.info(f"Executing task with max {max_steps} steps: {task}")
        
        step_results = []
        
        for step in range(max_steps):
            step_info = AgentStepInfo(step, max_steps)
            
            try:
                self.logger.info(f"Step {step + 1}/{max_steps}")
                
                # Execute single step
                step_result = await self._execute_single_step(task, step_info)
                step_results.append(step_result)
                
                # Check if task is complete
                if step_result.get("is_done", False):
                    self.logger.info(f"Task completed in {step + 1} steps")
                    return {
                        "success": True,
                        "steps": step_results,
                        "total_steps": step + 1,
                        "final_result": step_result
                    }
                
                # Reset consecutive failures on successful step
                self.state.consecutive_failures = 0
                
            except Exception as e:
                self.logger.error(f"Step {step + 1} failed: {e}")
                self.state.consecutive_failures += 1
                
                step_results.append({
                    "step": step + 1,
                    "error": str(e),
                    "success": False
                })
                
                # Check if we should stop due to too many failures
                if self.state.consecutive_failures >= self.settings.max_failures:
                    self.logger.error(f"Stopping due to {self.settings.max_failures} consecutive failures")
                    break
        
        # Task didn't complete within max steps
        return {
            "success": False,
            "steps": step_results,
            "total_steps": len(step_results),
            "error": f"Task did not complete within {max_steps} steps"
        }
    
    async def _execute_single_step(self, task: str, step_info: AgentStepInfo) -> Dict[str, Any]:
        """Execute a single step of the task."""
        # Get current page state
        page_info = await self._get_current_page_info()
        
        # Prepare context for LLM
        context = {
            "task": task,
            "step_number": step_info.step_number + 1,
            "max_steps": step_info.max_steps,
            "is_last_step": step_info.is_last_step(),
            "page_info": page_info,
            "available_tools": self.tools.get_available_tools() if self.tools else [],
            "execution_context": self.state.execution_context
        }
        
        # Generate action plan using LLM
        action_plan = await self._generate_action_plan(context)
        
        # Execute the planned actions
        execution_result = await self._execute_action_plan(action_plan)
        
        # Update execution context
        self.state.execution_context.update({
            "last_action": action_plan.get("action"),
            "last_result": execution_result,
            "step_number": step_info.step_number + 1
        })
        
        return execution_result
    
    async def _generate_action_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate action plan using LLM."""
        # This would use the LLM to generate a structured action plan
        # For now, return a simple plan structure
        return {
            "action": "navigate",
            "parameters": {"url": "https://example.com"},
            "reasoning": "Starting with navigation to example site"
        }
    
    async def _execute_action_plan(self, action_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the generated action plan."""
        action = action_plan.get("action")
        parameters = action_plan.get("parameters", {})
        
        try:
            if action == "navigate" and self.tools:
                success = await self.tools.navigate_to(parameters.get("url"))
                return {
                    "success": success,
                    "action": action,
                    "parameters": parameters,
                    "is_done": False
                }
            
            # Add more action handlers here
            return {
                "success": False,
                "error": f"Unknown action: {action}",
                "action": action,
                "parameters": parameters
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": action,
                "parameters": parameters
            }
    
    async def _get_current_page_info(self) -> Dict[str, Any]:
        """Get information about the current page."""
        try:
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                return {
                    "url": await self.browser_session.get_current_url(page),
                    "title": await self.browser_session.get_page_title(page),
                    "content_length": len(await self.browser_session.get_page_content(page))
                }
        except Exception as e:
            self.logger.warning(f"Failed to get page info: {e}")
        
        return {"url": "", "title": "", "content_length": 0}
    
    async def _take_error_screenshot(self) -> Optional[bytes]:
        """Take a screenshot for error debugging."""
        try:
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                screenshot_path = await self.browser_session.take_screenshot(page)
                if screenshot_path:
                    with open(screenshot_path, "rb") as f:
                        return f.read()
        except Exception as e:
            self.logger.warning(f"Failed to take error screenshot: {e}")
        return None
    
    async def _attempt_final_recovery(self, task: str, error: str) -> Dict[str, Any]:
        """Attempt final recovery after max failures."""
        self.logger.info("Attempting final recovery")
        
        try:
            # Simple recovery: take screenshot and return current state
            screenshot_path = None
            if self.browser_session and self.browser_session.is_initialized:
                page = await self.browser_session.get_page()
                screenshot_path = await self.browser_session.take_screenshot(page)
            
            return {
                "success": True,
                "recovery_attempt": True,
                "final_state": await self._get_current_page_info(),
                "screenshot": screenshot_path,
                "original_error": error
            }
            
        except Exception as e:
            return {
                "success": False,
                "recovery_attempt": True,
                "recovery_error": str(e),
                "original_error": error
            }
    
    async def _call_task_callbacks(self, event: str, task_data: Dict[str, Any]):
        """Call registered task callbacks."""
        callbacks = self.task_callbacks.get(event, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_data)
                else:
                    callback(task_data)
            except Exception as e:
                self.logger.warning(f"Task callback failed: {e}")
    
    async def _call_error_callbacks(self, error: Exception, task_data: Dict[str, Any]):
        """Call registered error callbacks."""
        for callback in self.error_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(error, task_data)
                else:
                    callback(error, task_data)
            except Exception as e:
                self.logger.warning(f"Error callback failed: {e}")
    
    def register_task_callback(self, event: str, callback: Callable):
        """Register a callback for task events."""
        if event not in self.task_callbacks:
            self.task_callbacks[event] = []
        self.task_callbacks[event].append(callback)
    
    def register_error_callback(self, callback: Callable):
        """Register a callback for errors."""
        self.error_callbacks.append(callback)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics."""
        stats = self.state.stats.copy()
        stats.update({
            "agent_id": self.state.agent_id,
            "is_initialized": self.is_initialized,
            "current_task": self.state.current_task is not None,
            "total_tasks": len(self.state.task_history),
            "success_rate": (
                self.state.stats["tasks_completed"] / 
                (self.state.stats["tasks_completed"] + self.state.stats["tasks_failed"])
                if (self.state.stats["tasks_completed"] + self.state.stats["tasks_failed"]) > 0
                else 0
            )
        })
        return stats
    
    def get_task_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get task execution history."""
        history = self.state.task_history.copy()
        if limit:
            history = history[-limit:]
        return history
    
    async def cleanup(self):
        """Clean up agent resources."""
        try:
            self.logger.info("Cleaning up enhanced agent resources")
            
            # Save current session
            if self.session_manager:
                await self.session_manager.save_session("default", self.browser_session.context)
            
            # Cleanup browser
            if self.browser_session:
                await self.browser_session.cleanup()
            
            # Close LLM connection
            if self.llm and hasattr(self.llm, 'close'):
                await self.llm.close()
            
            self.is_initialized = False
            self.logger.info("Enhanced agent cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
