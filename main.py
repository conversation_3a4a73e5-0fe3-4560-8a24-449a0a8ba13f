"""
Main entry point for the browser automation system.
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.agents import WebAgent
from src.utils import get_logger, config
from src.tools import CustomTools

logger = get_logger(__name__)


async def interactive_mode():
    """Run the agent in interactive mode."""
    print("🤖 Browser Automation System - Interactive Mode")
    print("=" * 50)
    print("Type 'help' for commands, 'quit' to exit")
    
    async with WebAgent(
        llm_provider=config.llm.default_provider,
        llm_model=config.llm.default_model
    ) as agent:
        
        print(f"✅ Agent initialized with {config.llm.default_provider}")
        print(f"📊 Agent ID: {agent.agent_id}")
        
        while True:
            try:
                user_input = input("\n🔤 Enter task: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    print_help()
                    continue
                
                if user_input.lower() == 'stats':
                    stats = agent.get_stats()
                    print(f"📊 Agent Stats: {stats}")
                    continue
                
                if user_input.lower() == 'history':
                    history = agent.get_task_history(limit=5)
                    print(f"📜 Recent History: {history}")
                    continue
                
                if user_input.lower() == 'screenshot':
                    screenshot_path = await agent.browser_manager.take_screenshot()
                    print(f"📸 Screenshot saved: {screenshot_path}")
                    continue
                
                if user_input.lower().startswith('navigate '):
                    url = user_input[9:].strip()
                    result = await agent.run(f"Navigate to {url}")
                    print(f"🌐 Navigation result: {result}")
                    continue
                
                # Execute the task
                print(f"🚀 Executing: {user_input}")
                result = await agent.run(user_input)
                print(f"✅ Result: {result}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Interactive mode error: {e}")


def print_help():
    """Print help information."""
    help_text = """
🤖 Browser Automation System - Commands:

Basic Commands:
  help                    - Show this help message
  quit/exit/q            - Exit the program
  stats                  - Show agent statistics
  history                - Show recent task history
  screenshot             - Take a screenshot
  navigate <url>         - Navigate to a URL

Task Examples:
  "Navigate to https://example.com"
  "Click on the login button"
  "Fill out the contact form with my information"
  "Extract all the product names from this page"
  "Scroll down to load more content"
  "Take a screenshot of the current page"

Advanced Examples:
  "Find all links on this page and click the first one"
  "Fill out the form with: name=John, email=<EMAIL>"
  "Extract the table data and save it as JSON"
  "Wait for the page to load completely then take a screenshot"
    """
    print(help_text)


async def run_script_mode(script_file: str):
    """Run tasks from a script file."""
    print(f"📜 Running script: {script_file}")
    
    script_path = Path(script_file)
    if not script_path.exists():
        print(f"❌ Script file not found: {script_file}")
        return
    
    async with WebAgent(
        llm_provider=config.llm.default_provider,
        llm_model=config.llm.default_model
    ) as agent:
        
        print(f"✅ Agent initialized")
        
        with open(script_path, 'r') as f:
            tasks = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        print(f"📋 Found {len(tasks)} tasks to execute")
        
        for i, task in enumerate(tasks, 1):
            try:
                print(f"\n🚀 Task {i}/{len(tasks)}: {task}")
                result = await agent.run(task)
                print(f"✅ Completed: {result.get('success', False)}")
                
            except Exception as e:
                print(f"❌ Task {i} failed: {e}")
                logger.error(f"Script task failed: {task} - {e}")


async def run_single_task(task: str):
    """Run a single task."""
    print(f"🎯 Running single task: {task}")
    
    async with WebAgent(
        llm_provider=config.llm.default_provider,
        llm_model=config.llm.default_model
    ) as agent:
        
        print(f"✅ Agent initialized")
        
        try:
            result = await agent.run(task)
            print(f"✅ Task completed: {result}")
            
            # Show final stats
            stats = agent.get_stats()
            print(f"📊 Final stats: {stats}")
            
        except Exception as e:
            print(f"❌ Task failed: {e}")
            logger.error(f"Single task failed: {task} - {e}")


async def run_examples():
    """Run example demonstrations."""
    print("🎪 Running example demonstrations...")
    
    try:
        # Import and run basic examples
        from src.examples.basic_automation import main as basic_main
        await basic_main()
        
        print("\n" + "="*50)
        
        # Import and run advanced examples
        from src.examples.advanced_features import main as advanced_main
        await advanced_main()
        
    except Exception as e:
        print(f"❌ Examples failed: {e}")
        logger.error(f"Examples failed: {e}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Browser Automation System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Interactive mode
  python main.py --task "Navigate to google.com"   # Single task
  python main.py --script tasks.txt                # Run script file
  python main.py --examples                        # Run examples
        """
    )
    
    parser.add_argument(
        '--task', '-t',
        help='Run a single task'
    )
    
    parser.add_argument(
        '--script', '-s',
        help='Run tasks from a script file'
    )
    
    parser.add_argument(
        '--examples', '-e',
        action='store_true',
        help='Run example demonstrations'
    )
    
    parser.add_argument(
        '--provider', '-p',
        choices=['gemini', 'openai', 'anthropic'],
        help='LLM provider to use'
    )
    
    parser.add_argument(
        '--model', '-m',
        help='LLM model to use'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='Run browser in headless mode'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    args = parser.parse_args()
    
    # Update config based on arguments
    if args.provider:
        config.llm.default_provider = args.provider
    
    if args.model:
        config.llm.default_model = args.model
    
    if args.headless:
        config.browser.headless = True
    
    if args.debug:
        config.logging.level = "DEBUG"
    
    # Print startup info
    print("🤖 Browser Automation System")
    print(f"🧠 LLM: {config.llm.default_provider} ({config.llm.default_model})")
    print(f"🌐 Browser: {'Headless' if config.browser.headless else 'GUI'}")
    print(f"📝 Logging: {config.logging.level}")
    
    # Run the appropriate mode
    try:
        if args.examples:
            asyncio.run(run_examples())
        elif args.task:
            asyncio.run(run_single_task(args.task))
        elif args.script:
            asyncio.run(run_script_mode(args.script))
        else:
            asyncio.run(interactive_mode())
            
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
