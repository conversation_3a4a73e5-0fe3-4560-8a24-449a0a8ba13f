# Enhanced Browser Automation System

A production-ready browser automation system based on the browser-use library patterns, featuring advanced agent capabilities, robust error handling, and comprehensive monitoring.

## 🚀 Features

### Core Capabilities
- **Enhanced Agent Architecture**: Production-ready agents with state management and event-driven patterns
- **Advanced Browser Management**: CDP integration, watchdogs, and automatic recovery
- **Robust LLM Integration**: Multi-provider support with structured output and error recovery
- **Comprehensive Tool System**: Action registry with proper error handling and monitoring
- **Event-Driven Architecture**: Real-time event system for browser and agent interactions
- **Production Configuration**: Environment-aware config with migration support

### Key Enhancements Over Basic Implementation
- **Lazy Import System**: Optimized startup performance
- **Browser Watchdogs**: Automatic browser health monitoring and recovery
- **CDP Integration**: Chrome DevTools Protocol for advanced browser control
- **Structured LLM Output**: Pydantic models for reliable AI responses
- **Comprehensive Logging**: Structured logging with agent-specific files
- **Performance Monitoring**: Real-time metrics and statistics
- **Session Management**: Persistent browser sessions with state recovery

## 📁 Project Structure

```
├── src/
│   ├── agents/
│   │   ├── enhanced_agent.py      # Production-ready agent with state management
│   │   └── base_agent.py          # Base agent implementation
│   ├── browser/
│   │   ├── enhanced_browser.py    # Advanced browser session with CDP
│   │   ├── browser_manager.py     # Basic browser management
│   │   └── session_manager.py     # Session persistence
│   ├── events/
│   │   ├── event_bus.py          # Event system core
│   │   ├── browser_events.py     # Browser-specific events
│   │   └── agent_events.py       # Agent-specific events
│   ├── llm/
│   │   ├── enhanced_llm.py       # Enhanced LLM with structured output
│   │   ├── llm_factory.py        # LLM provider factory
│   │   └── providers/            # LLM provider implementations
│   ├── tools/
│   │   ├── enhanced_tools.py     # Production tools with action registry
│   │   └── core_tools.py         # Basic tool implementations
│   ├── utils/
│   │   ├── enhanced_config.py    # Production configuration system
│   │   ├── agent_logger.py       # Enhanced logging utilities
│   │   └── error_handling.py     # Comprehensive error handling
│   └── examples/
│       └── production_examples.py # Real-world usage examples
├── enhanced_main.py              # Enhanced CLI interface
├── requirements.txt              # Dependencies
└── .env.example                 # Environment configuration template
```

## 🛠 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd browser-automation-system
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

3. **Set up environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

## 🚀 Quick Start

### Command Line Usage

1. **Execute a simple task**:
   ```bash
   python enhanced_main.py --task "Navigate to google.com and search for 'AI news'"
   ```

2. **Run in interactive mode**:
   ```bash
   python enhanced_main.py --interactive
   ```

3. **Run production examples**:
   ```bash
   # Run all examples
   python enhanced_main.py --examples
   
   # Run specific example
   python enhanced_main.py --examples --example web_scraping
   ```

4. **Show configuration**:
   ```bash
   python enhanced_main.py --config --show
   ```

### Programmatic Usage

```python
import asyncio
from src.agents.enhanced_agent import EnhancedAgent, AgentSettings
from src.browser.enhanced_browser import EnhancedBrowserSession

async def main():
    # Create enhanced browser session
    async with EnhancedBrowserSession() as browser_session:
        # Configure agent settings
        settings = AgentSettings(
            use_vision=True,
            max_failures=3,
            max_actions_per_step=4,
            use_thinking=True
        )
        
        # Create enhanced agent
        async with EnhancedAgent(
            task="Extract news headlines from a website",
            browser_session=browser_session,
            settings=settings
        ) as agent:
            
            # Execute task
            result = await agent.run(
                "Navigate to news.ycombinator.com and extract top 10 headlines",
                max_steps=15
            )
            
            print(f"Success: {result['success']}")
            print(f"Agent stats: {agent.get_stats()}")

asyncio.run(main())
```

## 🔧 Configuration

The system uses a sophisticated configuration system with environment variable support:

### Environment Variables

```bash
# LLM Configuration
OPENAI_API_KEY=your_openai_key
GOOGLE_API_KEY=your_google_key
ANTHROPIC_API_KEY=your_anthropic_key

# Browser Configuration
BROWSER_USE_HEADLESS=false
BROWSER_USE_ALLOWED_DOMAINS=example.com,google.com
BROWSER_USE_PROXY_URL=http://proxy:8080

# Logging
BROWSER_USE_LOGGING_LEVEL=info
BROWSER_USE_DEBUG_LOG_FILE=logs/debug.log
```

### Configuration Files

The system automatically creates and manages configuration files in `~/.config/browseruse/config.json` with database-style entries for browsers, LLMs, and agents.

## 📊 Monitoring and Events

### Event System

The system includes a comprehensive event system for monitoring:

```python
from src.events import default_event_bus
from src.events.browser_events import NavigationEvent

# Subscribe to navigation events
async def on_navigation(event: NavigationEvent):
    print(f"Navigated to: {event.target_url}")

default_event_bus.subscribe(NavigationEvent, on_navigation)
```

### Performance Monitoring

- **Agent Statistics**: Task completion rates, execution times, success rates
- **Browser Statistics**: Page loads, navigation counts, error rates
- **LLM Statistics**: Token usage, response times, costs
- **Event Statistics**: Event counts, handler performance

## 🎯 Production Examples

The system includes comprehensive examples demonstrating real-world usage:

### Web Scraping
```bash
python enhanced_main.py --examples --example web_scraping
```
Extracts news headlines from Hacker News with structured data output.

### Form Automation
```bash
python enhanced_main.py --examples --example form_automation
```
Fills and submits forms with validation and error handling.

### E-commerce
```bash
python enhanced_main.py --examples --example ecommerce
```
Searches and compares products with recommendation logic.

### Social Media Monitoring
```bash
python enhanced_main.py --examples --example social_media
```
Monitors trending topics and analyzes content patterns.

### Research Tasks
```bash
python enhanced_main.py --examples --example research
```
Multi-source research with information synthesis.

## 🔍 Advanced Features

### Browser Watchdogs
Automatic browser health monitoring with recovery:
- Health checks every 5 seconds
- Automatic session restart on failures
- Error recovery with exponential backoff

### CDP Integration
Chrome DevTools Protocol integration for:
- Performance metrics collection
- Network request monitoring
- Advanced browser control
- Real-time event handling

### Structured LLM Output
Pydantic models for reliable AI responses:
```python
from src.llm.enhanced_llm import ActionPlan, TaskEvaluation

# Generate structured action plan
plan = await llm.generate_structured(messages, ActionPlan)
print(f"Actions: {plan.actions}")
print(f"Confidence: {plan.confidence}")
```

### Action Registry
Decorator-based action system:
```python
from src.tools.enhanced_tools import action

@action(
    name="custom_action",
    description="Custom browser action",
    category="interaction"
)
async def custom_action(self, parameter: str) -> ActionResult:
    # Implementation
    pass
```

## 🐛 Error Handling

Comprehensive error handling with:
- **Automatic Retry Logic**: Exponential backoff for transient errors
- **Error Classification**: Recoverable vs non-recoverable errors
- **Context Preservation**: Full error context for debugging
- **Screenshot Capture**: Automatic screenshots on errors
- **Recovery Strategies**: Multiple recovery approaches

## 📝 Logging

Structured logging with:
- **Agent-specific logs**: Individual log files per agent
- **Event logging**: All events logged with context
- **Performance logging**: Execution times and statistics
- **Error logging**: Comprehensive error information with screenshots

## 🧪 Testing

Run the production examples to test the system:

```bash
# Test all functionality
python enhanced_main.py --examples

# Test specific components
python enhanced_main.py --task "Take a screenshot of google.com" --headless

# Interactive testing
python enhanced_main.py --interactive
```

## 🤝 Contributing

1. Follow the existing patterns and architecture
2. Add comprehensive error handling
3. Include event publishing for monitoring
4. Write production examples for new features
5. Update documentation

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Based on patterns from the [browser-use](https://github.com/browser-use/browser-use) library
- Inspired by production browser automation requirements
- Built with modern Python async/await patterns
