"""
Basic browser automation examples.
"""

import asyncio
import os
from pathlib import Path

# Add src to path for imports
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents import WebAgent
from utils import get_logger

logger = get_logger(__name__)


async def basic_navigation_example():
    """Example of basic navigation and page interaction."""
    print("🚀 Starting basic navigation example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a website
        result = await agent.run("Navigate to https://httpbin.org/forms/post")
        print(f"Navigation result: {result}")
        
        # Get page information
        page_info = await agent.get_page_info()
        print(f"Page info: {page_info}")
        
        # Take a screenshot
        screenshot_path = await agent.browser_manager.take_screenshot()
        print(f"Screenshot saved: {screenshot_path}")


async def form_filling_example():
    """Example of automated form filling."""
    print("📝 Starting form filling example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to form page
        await agent.run("Navigate to https://httpbin.org/forms/post")
        
        # Fill out the form
        form_data = {
            "custname": "<PERSON> Doe",
            "custtel": "555-1234",
            "custemail": "<EMAIL>",
            "size": "medium",
            "comments": "This is a test comment"
        }
        
        result = await agent.run(
            "Fill out the customer information form",
            form_data=form_data,
            submit=False  # Don't submit for demo
        )
        
        print(f"Form filling result: {result}")


async def data_extraction_example():
    """Example of data extraction from a webpage."""
    print("🔍 Starting data extraction example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page with data
        await agent.run("Navigate to https://httpbin.org/json")
        
        # Extract data from the page
        result = await agent.run(
            "Extract all the JSON data from this page",
            format="json"
        )
        
        print(f"Extracted data: {result}")


async def search_and_click_example():
    """Example of searching and clicking elements."""
    print("🔎 Starting search and click example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to httpbin
        await agent.run("Navigate to https://httpbin.org")
        
        # Click on a link
        result = await agent.run("Click on the 'HTTP Methods' link or similar navigation element")
        
        print(f"Click result: {result}")
        
        # Get the new page info
        page_info = await agent.get_page_info()
        print(f"New page: {page_info}")


async def multi_step_task_example():
    """Example of a multi-step automation task."""
    print("🔄 Starting multi-step task example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Complex task with multiple steps
        task = """
        1. Navigate to https://httpbin.org
        2. Find and click on any form-related link
        3. If there's a form, fill it with sample data
        4. Take a screenshot of the result
        """
        
        result = await agent.run(task)
        print(f"Multi-step task result: {result}")


async def error_handling_example():
    """Example of error handling in automation."""
    print("⚠️ Starting error handling example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        try:
            # Try to navigate to a non-existent page
            result = await agent.run("Navigate to https://this-site-does-not-exist-12345.com")
            print(f"Unexpected success: {result}")
            
        except Exception as e:
            print(f"Expected error caught: {e}")
            
            # Get agent stats to see error tracking
            stats = agent.get_stats()
            print(f"Agent stats: {stats}")


async def session_management_example():
    """Example of session saving and loading."""
    print("💾 Starting session management example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate and perform some actions
        await agent.run("Navigate to https://httpbin.org")
        
        # Save the session
        saved = await agent.save_session("demo_session")
        print(f"Session saved: {saved}")
        
        # Get session info
        sessions = agent.session_manager.list_sessions()
        print(f"Available sessions: {sessions}")


async def performance_monitoring_example():
    """Example of performance monitoring."""
    print("📊 Starting performance monitoring example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page
        await agent.run("Navigate to https://httpbin.org")
        
        # Get performance stats
        stats = agent.get_stats()
        print(f"Agent performance stats: {stats}")
        
        # Get task history
        history = agent.get_task_history(limit=5)
        print(f"Recent task history: {history}")


async def main():
    """Run all examples."""
    print("🤖 Browser Automation System - Basic Examples")
    print("=" * 50)
    
    # Check if environment is set up
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY environment variable not set!")
        print("Please set up your .env file with API keys.")
        return
    
    examples = [
        ("Basic Navigation", basic_navigation_example),
        ("Form Filling", form_filling_example),
        ("Data Extraction", data_extraction_example),
        ("Search and Click", search_and_click_example),
        ("Multi-step Task", multi_step_task_example),
        ("Error Handling", error_handling_example),
        ("Session Management", session_management_example),
        ("Performance Monitoring", performance_monitoring_example),
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n{'='*20} {name} {'='*20}")
            await example_func()
            print(f"✅ {name} completed successfully")
            
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            logger.error(f"Example {name} failed", exc_info=True)
        
        # Small delay between examples
        await asyncio.sleep(2)
    
    print("\n🎉 All examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
