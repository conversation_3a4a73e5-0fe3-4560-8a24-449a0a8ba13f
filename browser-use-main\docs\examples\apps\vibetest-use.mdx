---
title: "Vibetest-Use (Automated QA)"
description: "Run multi-agent Browser-Use tests to catch UI bugs, broken links, and accessibility issues before they ship."
icon: "bug"
mode: "wide"
---

<Note>
Requires **browser-use&nbsp; < v0.5.0** and Playwright Chromium. Currently getting an update to v0.7.6+. 
</Note>

<video
  controls
  className="w-full aspect-video rounded-xl"
  src="https://github.com/user-attachments/assets/6450b5b7-10e5-4019-82a4-6d726dbfbe1f">
</video>

## Features

1. Launches multiple headless (or visible) Browser-Use agents in parallel
2. Crawls your site and records screenshots, broken links & a11y issues
3. Works on production URLs *and* `localhost` dev servers
4. Simple natural-language prompts via MCP in Cursor / Claude Code

## Quick Start

```bash
# 1.  Create & activate env
uv venv --python 3.11
source .venv/bin/activate

# 2.  Install project
uv pip install -e .

# 3.  Install browser runtime once
playwright install chromium --with-deps --no-shell
```

### 1) <PERSON> Code

```bash
# Register the MCP server
claude mcp add vibetest /full/path/to/vibetest-use/.venv/bin/vibetest-mcp \
  -e GOOGLE_API_KEY="your_api_key"

# Inside a Claude chat
> /mcp
# ⎿  MCP Server Status
#    • vibetest: connected
```

### 2) Cursor (manual MCP entry)

1. Open **Settings → MCP**  
2. Click **Add Server** and paste:

```json
{
  "mcpServers": {
    "vibetest": {
      "command": "/full/path/to/vibetest-use/.venv/bin/vibetest-mcp",
      "env": {
        "GOOGLE_API_KEY": "your_api_key"
      }
    }
  }
}
```

## Basic Prompts
```
> Vibetest my website with 5 agents: browser-use.com
> Run vibetest on localhost:3000
> Run a headless vibetest on localhost:8080 with 10 agents
```

### Parameters
* **URL** – any `https` or `http` host or `localhost:port`
* **Agents** – `3` by default; more agents = deeper coverage
* **Headless** – say *headless* to hide the browser, omit to watch it live

## Requirements

* Python 3.11+
* Google API key (Gemini flash used for analysis)
* Cursor / Claude with MCP support

## Source Code

Full implementation: [https://github.com/browser-use/vibetest-use](https://github.com/browser-use/vibetest-use)
