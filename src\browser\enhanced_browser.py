"""
Enhanced browser management with CDP integration and watchdogs.
Based on browser-use patterns for production reliability.
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from uuid import uuid4

from playwright.async_api import (
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, async_playwright,
    <PERSON>Session, <PERSON>rror as PlaywrightError
)
from pydantic import BaseModel, Field

from ..events import EventBus, default_event_bus
from ..events.browser_events import (
    NavigationEvent, PageLoadEvent, ErrorEvent, PerformanceEvent
)
from ..utils.enhanced_config import ENHANCED_CONFIG

logger = logging.getLogger(__name__)


class BrowserWatchdog:
    """Watchdog for monitoring browser health and automatic recovery."""
    
    def __init__(self, browser_session: 'EnhancedBrowserSession'):
        self.browser_session = browser_session
        self.is_running = False
        self.check_interval = 5.0  # seconds
        self.last_health_check = time.time()
        self.consecutive_failures = 0
        self.max_failures = 3
        self._watchdog_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Start the watchdog monitoring."""
        if self.is_running:
            return
        
        self.is_running = True
        self._watchdog_task = asyncio.create_task(self._monitor_loop())
        logger.info("Browser watchdog started")
    
    async def stop(self):
        """Stop the watchdog monitoring."""
        self.is_running = False
        if self._watchdog_task:
            self._watchdog_task.cancel()
            try:
                await self._watchdog_task
            except asyncio.CancelledError:
                pass
        logger.info("Browser watchdog stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop."""
        while self.is_running:
            try:
                await asyncio.sleep(self.check_interval)
                
                if not self.is_running:
                    break
                
                # Perform health check
                is_healthy = await self._health_check()
                
                if is_healthy:
                    self.consecutive_failures = 0
                    self.last_health_check = time.time()
                else:
                    self.consecutive_failures += 1
                    logger.warning(f"Browser health check failed ({self.consecutive_failures}/{self.max_failures})")
                    
                    # Attempt recovery if too many failures
                    if self.consecutive_failures >= self.max_failures:
                        await self._attempt_recovery()
                        self.consecutive_failures = 0
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Watchdog monitoring error: {e}")
                await asyncio.sleep(1)  # Brief pause before retrying
    
    async def _health_check(self) -> bool:
        """Perform browser health check."""
        try:
            # Check if browser is still connected
            if not self.browser_session.browser:
                return False
            
            # Check if context is still valid
            if not self.browser_session.context:
                return False
            
            # Try to get pages
            pages = self.browser_session.context.pages
            if not pages:
                return False
            
            # Try to evaluate simple JavaScript on the first page
            page = pages[0]
            result = await page.evaluate("() => true")
            return result is True
            
        except Exception as e:
            logger.debug(f"Health check failed: {e}")
            return False
    
    async def _attempt_recovery(self):
        """Attempt to recover from browser failures."""
        logger.info("Attempting browser recovery")
        
        try:
            # Publish error event
            await default_event_bus.publish(ErrorEvent(
                browser_session_id=self.browser_session.session_id,
                error_type="browser_health_failure",
                error_message="Browser health check failed, attempting recovery",
                recoverable=True,
                retry_count=self.consecutive_failures
            ))
            
            # Try to restart the browser session
            await self.browser_session._restart_session()
            
            logger.info("Browser recovery completed successfully")
            
        except Exception as e:
            logger.error(f"Browser recovery failed: {e}")
            
            # Publish recovery failure event
            await default_event_bus.publish(ErrorEvent(
                browser_session_id=self.browser_session.session_id,
                error_type="browser_recovery_failure",
                error_message=f"Browser recovery failed: {e}",
                recoverable=False
            ))


class CDPManager:
    """Manager for Chrome DevTools Protocol sessions."""
    
    def __init__(self, page: Page):
        self.page = page
        self.cdp_session: Optional[CDPSession] = None
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.is_connected = False
    
    async def connect(self) -> bool:
        """Connect to CDP session."""
        try:
            self.cdp_session = await self.page.context.new_cdp_session(self.page)
            self.is_connected = True
            
            # Enable domains we might need
            await self.cdp_session.send("Runtime.enable")
            await self.cdp_session.send("Network.enable")
            await self.cdp_session.send("Performance.enable")
            
            logger.debug("CDP session connected and domains enabled")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect CDP session: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect CDP session."""
        if self.cdp_session:
            try:
                await self.cdp_session.detach()
            except Exception as e:
                logger.warning(f"Error disconnecting CDP session: {e}")
            finally:
                self.cdp_session = None
                self.is_connected = False
    
    async def send_command(self, method: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Send CDP command."""
        if not self.cdp_session:
            raise RuntimeError("CDP session not connected")
        
        try:
            result = await self.cdp_session.send(method, params or {})
            return result
        except Exception as e:
            logger.error(f"CDP command failed: {method} - {e}")
            raise
    
    def add_event_handler(self, event_name: str, handler: Callable):
        """Add handler for CDP events."""
        if event_name not in self.event_handlers:
            self.event_handlers[event_name] = []
        self.event_handlers[event_name].append(handler)
        
        if self.cdp_session:
            self.cdp_session.on(event_name, handler)
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics via CDP."""
        try:
            metrics = await self.send_command("Performance.getMetrics")
            return {metric['name']: metric['value'] for metric in metrics.get('metrics', [])}
        except Exception as e:
            logger.warning(f"Failed to get performance metrics: {e}")
            return {}


class EnhancedBrowserSession:
    """Enhanced browser session with CDP integration and monitoring."""
    
    def __init__(
        self,
        session_id: Optional[str] = None,
        event_bus: Optional[EventBus] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        self.session_id = session_id or str(uuid4())
        self.event_bus = event_bus or default_event_bus
        self.config = config or ENHANCED_CONFIG.load_config()
        
        # Browser components
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.pages: List[Page] = []
        
        # Enhanced features
        self.watchdog = BrowserWatchdog(self)
        self.cdp_managers: Dict[str, CDPManager] = {}
        
        # State tracking
        self.is_initialized = False
        self.start_time = time.time()
        self.stats = {
            "pages_created": 0,
            "navigation_count": 0,
            "error_count": 0,
            "total_load_time": 0.0
        }
    
    async def initialize(self) -> bool:
        """Initialize the enhanced browser session."""
        try:
            logger.info(f"Initializing enhanced browser session {self.session_id}")
            
            # Start Playwright
            self.playwright = await async_playwright().start()
            
            # Get browser configuration
            browser_config = self.config.get('browser_profile', {})
            
            # Launch browser with enhanced options
            browser_args = self._get_browser_args(browser_config)
            
            self.browser = await self.playwright.chromium.launch(
                headless=browser_config.get('headless', False),
                args=browser_args,
                timeout=browser_config.get('timeout', 30000)
            )
            
            # Create context with enhanced settings
            context_options = self._get_context_options(browser_config)
            self.context = await self.browser.new_context(**context_options)
            
            # Set up context event handlers
            await self._setup_context_handlers()
            
            # Create initial page
            page = await self.context.new_page()
            self.pages.append(page)
            self.stats["pages_created"] += 1
            
            # Set up CDP for the page
            await self._setup_cdp_for_page(page)
            
            # Start watchdog
            await self.watchdog.start()
            
            self.is_initialized = True
            logger.info(f"Enhanced browser session {self.session_id} initialized successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize browser session: {e}")
            await self._cleanup_on_error()
            return False
    
    def _get_browser_args(self, config: Dict[str, Any]) -> List[str]:
        """Get browser launch arguments from configuration."""
        args = []
        
        # Basic stealth arguments
        if config.get('disable_blink_features', True):
            args.append("--disable-blink-features=AutomationControlled")
        
        if config.get('disable_extensions', False):
            args.append("--disable-extensions")
        
        if config.get('disable_plugins', True):
            args.append("--disable-plugins")
        
        # Docker-specific arguments
        if config.get('disable_dev_shm_usage'):
            args.append("--disable-dev-shm-usage")
        
        if config.get('disable_gpu'):
            args.append("--disable-gpu")
        
        if config.get('no_sandbox'):
            args.append("--no-sandbox")
        
        # Proxy settings
        if config.get('proxy_server'):
            args.append(f"--proxy-server={config['proxy_server']}")
        
        if config.get('proxy_bypass'):
            args.append(f"--proxy-bypass-list={config['proxy_bypass']}")
        
        # Additional performance and stealth arguments
        args.extend([
            "--exclude-switches=enable-automation",
            "--disable-extensions-http-throttling",
            "--disable-component-extensions-with-background-pages",
            "--disable-default-apps",
            "--disable-sync",
            "--no-default-browser-check",
            "--no-first-run",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
        ])
        
        return args
    
    def _get_context_options(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get browser context options from configuration."""
        options = {
            "viewport": {
                "width": config.get('viewport_width', 1920),
                "height": config.get('viewport_height', 1080)
            },
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
        
        # Downloads path
        if config.get('downloads_path'):
            options["accept_downloads"] = True
            options["downloads_path"] = config['downloads_path']
        
        # User data directory
        if config.get('user_data_dir'):
            options["storage_state"] = str(Path(config['user_data_dir']) / "state.json")
        
        # Proxy authentication
        proxy_config = config.get('proxy', {})
        if proxy_config.get('username') and proxy_config.get('password'):
            options["http_credentials"] = {
                "username": proxy_config['username'],
                "password": proxy_config['password']
            }
        
        return options
    
    async def _setup_context_handlers(self):
        """Set up event handlers for the browser context."""
        if not self.context:
            return
        
        # Page creation handler
        self.context.on("page", self._on_page_created)
        
        # Request/response handlers for monitoring
        self.context.on("request", self._on_request)
        self.context.on("response", self._on_response)
    
    async def _on_page_created(self, page: Page):
        """Handle new page creation."""
        self.pages.append(page)
        self.stats["pages_created"] += 1
        
        # Set up CDP for new page
        await self._setup_cdp_for_page(page)
        
        logger.debug(f"New page created: {page.url}")
    
    async def _on_request(self, request):
        """Handle network requests."""
        # Could be used for request monitoring/modification
        pass
    
    async def _on_response(self, response):
        """Handle network responses."""
        # Could be used for response monitoring
        pass
    
    async def _setup_cdp_for_page(self, page: Page):
        """Set up CDP manager for a page."""
        try:
            cdp_manager = CDPManager(page)
            success = await cdp_manager.connect()
            
            if success:
                self.cdp_managers[str(page)] = cdp_manager
                
                # Set up performance monitoring
                await self._setup_performance_monitoring(cdp_manager)
                
                logger.debug(f"CDP set up for page: {page.url}")
            
        except Exception as e:
            logger.warning(f"Failed to set up CDP for page: {e}")
    
    async def _setup_performance_monitoring(self, cdp_manager: CDPManager):
        """Set up performance monitoring via CDP."""
        try:
            # Enable performance timeline
            await cdp_manager.send_command("Performance.enable", {"timeDomain": "timeTicks"})
            
            # Set up periodic performance collection
            asyncio.create_task(self._collect_performance_metrics(cdp_manager))
            
        except Exception as e:
            logger.warning(f"Failed to set up performance monitoring: {e}")
    
    async def _collect_performance_metrics(self, cdp_manager: CDPManager):
        """Periodically collect performance metrics."""
        while cdp_manager.is_connected:
            try:
                await asyncio.sleep(30)  # Collect every 30 seconds
                
                metrics = await cdp_manager.get_performance_metrics()
                
                if metrics:
                    # Publish performance event
                    await self.event_bus.publish(PerformanceEvent(
                        browser_session_id=self.session_id,
                        metric_name="browser_performance",
                        metric_value=metrics.get("JSHeapUsedSize", 0),
                        metric_unit="bytes",
                        additional_metrics=metrics
                    ))
                
            except Exception as e:
                logger.debug(f"Performance collection error: {e}")
                break
    
    async def navigate_to(self, url: str, page_index: int = 0, wait_until: str = "domcontentloaded") -> bool:
        """Navigate to URL with enhanced monitoring."""
        if not self.is_initialized or page_index >= len(self.pages):
            return False
        
        page = self.pages[page_index]
        start_time = time.time()
        
        try:
            # Publish navigation start event
            nav_event = NavigationEvent(
                browser_session_id=self.session_id,
                target_url=url,
                navigation_type="navigate",
                wait_until=wait_until,
                page_url=page.url,
                page_title=await page.title() if page.url else None
            )
            await self.event_bus.publish(nav_event)
            
            # Perform navigation
            response = await page.goto(url, wait_until=wait_until)
            
            # Calculate load time
            load_time = time.time() - start_time
            self.stats["navigation_count"] += 1
            self.stats["total_load_time"] += load_time
            
            # Check if navigation was successful
            success = response is not None and response.ok
            
            # Update navigation event with result
            nav_event.success = success
            if not success and response:
                nav_event.error = f"HTTP {response.status}"
            
            # Publish page load event
            await self.event_bus.publish(PageLoadEvent(
                browser_session_id=self.session_id,
                page_url=url,
                page_title=await page.title(),
                load_time=load_time,
                load_state=wait_until
            ))
            
            logger.info(f"Navigation to {url} {'succeeded' if success else 'failed'} in {load_time:.2f}s")
            
            return success
            
        except Exception as e:
            load_time = time.time() - start_time
            self.stats["error_count"] += 1
            
            # Publish error event
            await self.event_bus.publish(ErrorEvent(
                browser_session_id=self.session_id,
                page_url=url,
                error_type="navigation_error",
                error_message=str(e),
                recoverable=True
            ))
            
            logger.error(f"Navigation to {url} failed: {e}")
            return False
    
    async def get_current_page(self, page_index: int = 0) -> Optional[Page]:
        """Get current page."""
        if not self.is_initialized or page_index >= len(self.pages):
            return None
        return self.pages[page_index]
    
    async def take_screenshot(self, page_index: int = 0, full_page: bool = True) -> Optional[str]:
        """Take screenshot with enhanced options."""
        page = await self.get_current_page(page_index)
        if not page:
            return None
        
        try:
            timestamp = int(time.time())
            screenshot_path = f"logs/screenshot_{self.session_id}_{timestamp}.png"
            
            # Ensure directory exists
            Path(screenshot_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Take screenshot
            await page.screenshot(path=screenshot_path, full_page=full_page)
            
            logger.debug(f"Screenshot saved: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return None
    
    async def _restart_session(self):
        """Restart the browser session for recovery."""
        logger.info("Restarting browser session for recovery")
        
        # Stop watchdog temporarily
        await self.watchdog.stop()
        
        # Clean up current session
        await self._cleanup_resources()
        
        # Reinitialize
        success = await self.initialize()
        
        if not success:
            raise RuntimeError("Failed to restart browser session")
        
        logger.info("Browser session restarted successfully")
    
    async def _cleanup_on_error(self):
        """Clean up resources when initialization fails."""
        try:
            await self._cleanup_resources()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    async def _cleanup_resources(self):
        """Clean up all browser resources."""
        # Stop watchdog
        if self.watchdog.is_running:
            await self.watchdog.stop()
        
        # Disconnect CDP sessions
        for cdp_manager in self.cdp_managers.values():
            await cdp_manager.disconnect()
        self.cdp_managers.clear()
        
        # Close pages
        for page in self.pages:
            try:
                await page.close()
            except Exception:
                pass
        self.pages.clear()
        
        # Close context
        if self.context:
            try:
                await self.context.close()
            except Exception:
                pass
            self.context = None
        
        # Close browser
        if self.browser:
            try:
                await self.browser.close()
            except Exception:
                pass
            self.browser = None
        
        # Stop Playwright
        if self.playwright:
            try:
                await self.playwright.stop()
            except Exception:
                pass
            self.playwright = None
        
        self.is_initialized = False
    
    async def cleanup(self):
        """Clean up the browser session."""
        logger.info(f"Cleaning up browser session {self.session_id}")
        await self._cleanup_resources()
        
        # Log final stats
        uptime = time.time() - self.start_time
        logger.info(f"Session {self.session_id} stats: {self.stats}, uptime: {uptime:.2f}s")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get session statistics."""
        uptime = time.time() - self.start_time
        stats = self.stats.copy()
        stats.update({
            "session_id": self.session_id,
            "uptime": uptime,
            "is_initialized": self.is_initialized,
            "active_pages": len(self.pages),
            "cdp_sessions": len(self.cdp_managers),
            "watchdog_running": self.watchdog.is_running
        })
        return stats
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
