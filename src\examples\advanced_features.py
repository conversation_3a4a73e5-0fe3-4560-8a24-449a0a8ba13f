"""
Advanced browser automation features and examples.
"""

import asyncio
import json
from pathlib import Path

# Add src to path for imports
import sys
sys.path.append(str(Path(__file__).parent.parent))

from agents import WebAgent
from tools import CustomTools
from utils import get_logger

logger = get_logger(__name__)


async def smart_form_filling_example():
    """Example of intelligent form filling."""
    print("🧠 Starting smart form filling example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a form page
        await agent.run("Navigate to https://httpbin.org/forms/post")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Smart form filling with automatic field detection
        form_data = {
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "phone": "555-0123",
            "company": "Acme Corp",
            "message": "Hello, this is a test message from the automation system."
        }
        
        result = await custom_tools.smart_form_filler(form_data)
        print(f"Smart form filling result: {result}")


async def table_extraction_example():
    """Example of extracting data from HTML tables."""
    print("📊 Starting table extraction example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page with tables (using a demo page)
        await agent.run("Navigate to https://www.w3schools.com/html/html_tables.asp")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Extract table data
        tables = await custom_tools.table_extractor("table")
        
        print(f"Extracted {len(tables)} tables:")
        for i, table in enumerate(tables):
            print(f"Table {i + 1}: {table['row_count']} rows, Headers: {table['headers']}")
            if table['data']:
                print(f"Sample row: {table['data'][0]}")


async def infinite_scroll_example():
    """Example of handling infinite scroll pages."""
    print("♾️ Starting infinite scroll example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page (simulating infinite scroll)
        await agent.run("Navigate to https://httpbin.org")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Handle infinite scroll
        result = await custom_tools.infinite_scroll_handler(max_scrolls=3, scroll_pause=1.0)
        print(f"Infinite scroll result: {result}")


async def cookie_management_example():
    """Example of cookie management."""
    print("🍪 Starting cookie management example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page
        await agent.run("Navigate to https://httpbin.org/cookies/set/test_cookie/test_value")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Get current cookies
        cookies_result = await custom_tools.cookie_manager("get")
        print(f"Current cookies: {cookies_result}")
        
        # Set custom cookies
        new_cookies = [
            {
                "name": "automation_test",
                "value": "browser_use_demo",
                "domain": "httpbin.org",
                "path": "/"
            }
        ]
        
        set_result = await custom_tools.cookie_manager("set", cookies=new_cookies)
        print(f"Set cookies result: {set_result}")


async def performance_monitoring_example():
    """Example of performance monitoring."""
    print("⚡ Starting performance monitoring example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page
        await agent.run("Navigate to https://httpbin.org")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Monitor performance
        perf_result = await custom_tools.performance_monitor()
        print(f"Performance metrics: {perf_result}")


async def accessibility_check_example():
    """Example of accessibility checking."""
    print("♿ Starting accessibility check example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page
        await agent.run("Navigate to https://httpbin.org")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Check accessibility
        a11y_result = await custom_tools.accessibility_checker()
        print(f"Accessibility check result: {a11y_result}")


async def element_highlighting_example():
    """Example of element highlighting for debugging."""
    print("🎯 Starting element highlighting example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page
        await agent.run("Navigate to https://httpbin.org")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Highlight elements for debugging
        await custom_tools.element_highlighter("h1", duration=3.0)
        print("Highlighted h1 elements for 3 seconds")
        
        # Take screenshot to capture highlighting
        screenshot_path = await agent.browser_manager.take_screenshot()
        print(f"Screenshot with highlighting: {screenshot_path}")


async def ajax_handling_example():
    """Example of handling AJAX requests."""
    print("🔄 Starting AJAX handling example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Navigate to a page that might have AJAX
        await agent.run("Navigate to https://httpbin.org/delay/2")
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        # Wait for AJAX to complete
        ajax_complete = await custom_tools.wait_for_ajax(timeout=10000)
        print(f"AJAX completion status: {ajax_complete}")


async def complex_workflow_example():
    """Example of a complex multi-step workflow."""
    print("🔧 Starting complex workflow example...")
    
    async with WebAgent(
        llm_provider="gemini",
        llm_model="gemini-pro"
    ) as agent:
        
        # Initialize custom tools
        custom_tools = CustomTools(agent.browser_manager, agent.tools, logger)
        
        workflow_steps = [
            "Navigate to https://httpbin.org",
            "Take a screenshot of the homepage",
            "Click on any available link",
            "Extract any visible data from the new page",
            "Check page performance metrics",
            "Save the session for later use"
        ]
        
        results = []
        
        for i, step in enumerate(workflow_steps, 1):
            try:
                print(f"Step {i}: {step}")
                
                if "screenshot" in step.lower():
                    screenshot_path = await agent.browser_manager.take_screenshot()
                    result = {"action": "screenshot", "path": screenshot_path}
                elif "performance" in step.lower():
                    result = await custom_tools.performance_monitor()
                elif "save session" in step.lower():
                    saved = await agent.save_session(f"complex_workflow_{i}")
                    result = {"action": "save_session", "success": saved}
                else:
                    result = await agent.run(step)
                
                results.append({"step": i, "description": step, "result": result})
                print(f"✅ Step {i} completed")
                
            except Exception as e:
                print(f"❌ Step {i} failed: {e}")
                results.append({"step": i, "description": step, "error": str(e)})
        
        print(f"\nWorkflow completed. Results summary:")
        for result in results:
            status = "✅" if "error" not in result else "❌"
            print(f"{status} Step {result['step']}: {result['description']}")


async def main():
    """Run all advanced examples."""
    print("🚀 Browser Automation System - Advanced Features")
    print("=" * 60)
    
    examples = [
        ("Smart Form Filling", smart_form_filling_example),
        ("Table Extraction", table_extraction_example),
        ("Infinite Scroll Handling", infinite_scroll_example),
        ("Cookie Management", cookie_management_example),
        ("Performance Monitoring", performance_monitoring_example),
        ("Accessibility Checking", accessibility_check_example),
        ("Element Highlighting", element_highlighting_example),
        ("AJAX Handling", ajax_handling_example),
        ("Complex Workflow", complex_workflow_example),
    ]
    
    for name, example_func in examples:
        try:
            print(f"\n{'='*20} {name} {'='*20}")
            await example_func()
            print(f"✅ {name} completed successfully")
            
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            logger.error(f"Advanced example {name} failed", exc_info=True)
        
        # Small delay between examples
        await asyncio.sleep(2)
    
    print("\n🎉 All advanced examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
