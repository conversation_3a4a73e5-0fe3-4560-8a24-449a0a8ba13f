"""
Enhanced LLM system with production-ready patterns.
"""

import asyncio
import json
import logging
import time
from typing import Optional, Dict, Any, List, Union, Type
from abc import ABC, abstractmethod
from pydantic import BaseModel, Field

from ..events import default_event_bus
from ..events.agent_events import LLMInteractionEvent

logger = logging.getLogger(__name__)


class LLMMessage(BaseModel):
    """Structured message for LLM interactions."""
    
    role: str = Field(...)  # system, user, assistant
    content: str = Field(...)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: float = Field(default_factory=time.time)


class LLMResponse(BaseModel):
    """Structured response from LLM."""
    
    content: str = Field(...)
    model: str = Field(...)
    provider: str = Field(...)
    usage: Optional[Dict[str, int]] = Field(default=None)
    response_time: float = Field(...)
    cost: Optional[float] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: float = Field(default_factory=time.time)


class StructuredOutput(BaseModel):
    """Base class for structured LLM outputs."""
    
    reasoning: Optional[str] = Field(default=None)
    confidence: Optional[float] = Field(default=None)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ActionPlan(StructuredOutput):
    """Structured action plan from LLM."""
    
    actions: List[Dict[str, Any]] = Field(default_factory=list)
    priority: int = Field(default=0)
    estimated_time: Optional[float] = Field(default=None)
    success_criteria: List[str] = Field(default_factory=list)


class TaskEvaluation(StructuredOutput):
    """Structured task evaluation from LLM."""
    
    success: bool = Field(...)
    completion_percentage: float = Field(default=0.0)
    issues_found: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    next_steps: List[str] = Field(default_factory=list)


class EnhancedBaseLLMProvider(ABC):
    """Enhanced base class for LLM providers with production features."""
    
    def __init__(
        self,
        model: str,
        api_key: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        **kwargs
    ):
        self.model = model
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.provider_name = self.__class__.__name__.replace("Provider", "").lower()
        
        # Statistics
        self.stats = {
            "requests_made": 0,
            "requests_failed": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "average_response_time": 0.0,
            "created_at": time.time()
        }
        
        # Message history
        self.conversation_history: List[LLMMessage] = []
        self.max_history_length = kwargs.get('max_history_length', 100)
        
        # Rate limiting
        self.rate_limit_requests = kwargs.get('rate_limit_requests', 60)  # per minute
        self.rate_limit_window = 60  # seconds
        self.request_timestamps: List[float] = []
    
    @abstractmethod
    async def _generate_response_impl(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Implementation-specific response generation."""
        pass
    
    async def generate_response(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Generate response with enhanced features."""
        return await self.generate_with_retry(messages, **kwargs)
    
    async def chat(
        self,
        message: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Simple chat interface."""
        messages = []
        
        if system_prompt:
            messages.append(LLMMessage(role="system", content=system_prompt))
        
        messages.append(LLMMessage(role="user", content=message))
        
        return await self.generate_response(messages, **kwargs)
    
    async def generate_structured(
        self,
        messages: List[LLMMessage],
        output_schema: Type[StructuredOutput],
        **kwargs
    ) -> StructuredOutput:
        """Generate structured output using Pydantic models."""
        # Add schema instructions to the system message
        schema_prompt = self._create_schema_prompt(output_schema)
        
        # Find or create system message
        system_message = None
        for msg in messages:
            if msg.role == "system":
                system_message = msg
                break
        
        if system_message:
            system_message.content += f"\n\n{schema_prompt}"
        else:
            messages.insert(0, LLMMessage(role="system", content=schema_prompt))
        
        # Generate response
        response = await self.generate_response(messages, **kwargs)
        
        # Parse structured output
        try:
            # Try to extract JSON from response
            content = response.content.strip()
            
            # Look for JSON block
            if "```json" in content:
                start = content.find("```json") + 7
                end = content.find("```", start)
                if end != -1:
                    json_content = content[start:end].strip()
                else:
                    json_content = content[start:].strip()
            elif content.startswith("{") and content.endswith("}"):
                json_content = content
            else:
                # Try to find JSON-like structure
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    json_content = json_match.group()
                else:
                    raise ValueError("No JSON structure found in response")
            
            # Parse and validate
            data = json.loads(json_content)
            return output_schema(**data)
            
        except Exception as e:
            logger.error(f"Failed to parse structured output: {e}")
            logger.debug(f"Response content: {response.content}")
            
            # Return a basic instance with error info
            return output_schema(
                reasoning=f"Failed to parse structured output: {e}",
                confidence=0.0,
                metadata={"parse_error": str(e), "raw_response": response.content}
            )
    
    def _create_schema_prompt(self, output_schema: Type[StructuredOutput]) -> str:
        """Create prompt for structured output."""
        schema_dict = output_schema.model_json_schema()
        
        prompt = f"""
You must respond with a valid JSON object that matches this exact schema:

{json.dumps(schema_dict, indent=2)}

Important:
- Your response must be valid JSON
- Include all required fields
- Use the exact field names and types specified
- You can wrap the JSON in ```json code blocks if needed
- Provide reasoning for your decisions in the 'reasoning' field
- Set confidence between 0.0 and 1.0 based on your certainty
"""
        return prompt
    
    async def generate_with_retry(
        self,
        messages: List[LLMMessage],
        **kwargs
    ) -> LLMResponse:
        """Generate response with retry logic and rate limiting."""
        # Check rate limiting
        await self._check_rate_limit()
        
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                start_time = time.time()
                
                # Generate response
                response = await asyncio.wait_for(
                    self._generate_response_impl(messages, **kwargs),
                    timeout=self.timeout
                )
                
                # Update statistics
                self._update_stats(response, time.time() - start_time)
                
                # Add to conversation history
                self._add_to_history(messages, response)
                
                # Publish interaction event
                await self._publish_interaction_event(messages, response, success=True)
                
                return response
                
            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"LLM request timeout (attempt {attempt + 1}/{self.max_retries})")
                
            except Exception as e:
                last_error = e
                logger.warning(f"LLM request failed (attempt {attempt + 1}/{self.max_retries}): {e}")
                
                # Exponential backoff
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
        
        # All retries failed
        self.stats["requests_failed"] += 1
        
        # Publish failure event
        await self._publish_interaction_event(messages, None, success=False, error=str(last_error))
        
        raise RuntimeError(f"LLM request failed after {self.max_retries} attempts: {last_error}")
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        
        # Remove old timestamps outside the window
        self.request_timestamps = [
            ts for ts in self.request_timestamps
            if current_time - ts < self.rate_limit_window
        ]
        
        # Check if we're at the limit
        if len(self.request_timestamps) >= self.rate_limit_requests:
            # Calculate wait time
            oldest_request = min(self.request_timestamps)
            wait_time = self.rate_limit_window - (current_time - oldest_request)
            
            if wait_time > 0:
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
        
        # Add current request timestamp
        self.request_timestamps.append(current_time)
    
    def _update_stats(self, response: LLMResponse, response_time: float):
        """Update provider statistics."""
        self.stats["requests_made"] += 1
        
        if response.usage:
            self.stats["total_tokens"] += response.usage.get("total_tokens", 0)
        
        if response.cost:
            self.stats["total_cost"] += response.cost
        
        # Update average response time
        current_avg = self.stats["average_response_time"]
        request_count = self.stats["requests_made"]
        self.stats["average_response_time"] = (
            (current_avg * (request_count - 1) + response_time) / request_count
        )
    
    def _add_to_history(self, messages: List[LLMMessage], response: LLMResponse):
        """Add messages and response to conversation history."""
        # Add input messages
        for message in messages:
            if message.role != "system":  # Don't store system messages in history
                self.conversation_history.append(message)
        
        # Add response
        self.conversation_history.append(LLMMessage(
            role="assistant",
            content=response.content,
            metadata={"model": response.model, "provider": response.provider}
        ))
        
        # Trim history if too long
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    async def _publish_interaction_event(
        self,
        messages: List[LLMMessage],
        response: Optional[LLMResponse],
        success: bool,
        error: Optional[str] = None
    ):
        """Publish LLM interaction event."""
        try:
            event = LLMInteractionEvent(
                agent_id="unknown",  # Will be set by agent if available
                llm_provider=self.provider_name,
                llm_model=self.model,
                prompt_tokens=sum(len(msg.content.split()) for msg in messages),
                completion_tokens=len(response.content.split()) if response else 0,
                total_tokens=sum(len(msg.content.split()) for msg in messages) + (len(response.content.split()) if response else 0),
                response_time=response.response_time if response else 0.0,
                cost=response.cost if response else None,
                success=success,
                error=error
            )
            
            await default_event_bus.publish(event)
            
        except Exception as e:
            logger.warning(f"Failed to publish LLM interaction event: {e}")
    
    def get_conversation_history(self, limit: Optional[int] = None) -> List[LLMMessage]:
        """Get conversation history."""
        history = self.conversation_history.copy()
        if limit:
            history = history[-limit:]
        return history
    
    def clear_history(self):
        """Clear conversation history."""
        self.conversation_history.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get provider statistics."""
        stats = self.stats.copy()
        stats.update({
            "provider": self.provider_name,
            "model": self.model,
            "history_length": len(self.conversation_history),
            "uptime": time.time() - self.stats["created_at"],
            "rate_limit_requests": self.rate_limit_requests,
            "current_rate": len(self.request_timestamps)
        })
        return stats
    
    async def close(self):
        """Close the provider and clean up resources."""
        self.conversation_history.clear()
        self.request_timestamps.clear()
        logger.info(f"Closed LLM provider: {self.provider_name} ({self.model})")


class MessageManager:
    """Manager for LLM conversation messages."""
    
    def __init__(self, max_context_length: int = 8000):
        self.messages: List[LLMMessage] = []
        self.max_context_length = max_context_length
        self.system_message: Optional[LLMMessage] = None
    
    def set_system_message(self, content: str):
        """Set the system message."""
        self.system_message = LLMMessage(role="system", content=content)
    
    def add_user_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a user message."""
        message = LLMMessage(
            role="user",
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self._trim_context()
    
    def add_assistant_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add an assistant message."""
        message = LLMMessage(
            role="assistant",
            content=content,
            metadata=metadata or {}
        )
        self.messages.append(message)
        self._trim_context()
    
    def get_messages(self) -> List[LLMMessage]:
        """Get all messages including system message."""
        messages = []
        if self.system_message:
            messages.append(self.system_message)
        messages.extend(self.messages)
        return messages
    
    def _trim_context(self):
        """Trim context to stay within limits."""
        total_length = sum(len(msg.content) for msg in self.messages)
        
        while total_length > self.max_context_length and len(self.messages) > 1:
            # Remove oldest non-system message
            removed = self.messages.pop(0)
            total_length -= len(removed.content)
    
    def clear(self):
        """Clear all messages except system message."""
        self.messages.clear()
    
    def get_context_info(self) -> Dict[str, Any]:
        """Get information about the current context."""
        total_length = sum(len(msg.content) for msg in self.messages)
        system_length = len(self.system_message.content) if self.system_message else 0
        
        return {
            "message_count": len(self.messages),
            "has_system_message": self.system_message is not None,
            "total_length": total_length,
            "system_length": system_length,
            "context_usage": total_length / self.max_context_length,
            "max_context_length": self.max_context_length
        }
