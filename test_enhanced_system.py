"""
Test script for the enhanced browser automation system.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.agents.enhanced_agent import EnhancedAgent, AgentSettings
from src.browser.enhanced_browser import EnhancedBrowserSession
from src.tools.enhanced_tools import EnhancedBrowserTools
from src.events import default_event_bus
from src.events.browser_events import NavigationEvent, ClickEvent
from src.events.agent_events import TaskStartEvent, TaskCompleteEvent
from src.utils.enhanced_config import ENHANCED_CONFIG

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_functionality():
    """Test basic system functionality."""
    logger.info("Testing basic functionality")
    
    try:
        # Test configuration loading
        config = ENHANCED_CONFIG.load_config()
        logger.info(f"Configuration loaded: {len(config)} sections")
        
        # Test event system
        events_received = []
        
        async def on_navigation(event: NavigationEvent):
            events_received.append(("navigation", event.target_url))
        
        async def on_task_start(event: TaskStartEvent):
            events_received.append(("task_start", event.task_description))
        
        default_event_bus.subscribe(NavigationEvent, on_navigation)
        default_event_bus.subscribe(TaskStartEvent, on_task_start)
        
        # Test browser session
        async with EnhancedBrowserSession() as browser_session:
            logger.info("Browser session created successfully")
            
            # Test navigation
            success = await browser_session.navigate_to("https://example.com")
            logger.info(f"Navigation test: {'PASS' if success else 'FAIL'}")
            
            # Test tools
            tools = EnhancedBrowserTools(browser_session)
            available_actions = tools.get_available_actions()
            logger.info(f"Available actions: {len(available_actions)}")
            
            # Test screenshot
            screenshot_path = await browser_session.take_screenshot()
            logger.info(f"Screenshot test: {'PASS' if screenshot_path else 'FAIL'}")
            
            # Get stats
            browser_stats = browser_session.get_stats()
            tools_stats = tools.get_stats()
            
            logger.info(f"Browser stats: {browser_stats}")
            logger.info(f"Tools stats: {tools_stats}")
        
        # Check events
        logger.info(f"Events received: {len(events_received)}")
        for event_type, data in events_received:
            logger.info(f"  {event_type}: {data}")
        
        return True
        
    except Exception as e:
        logger.error(f"Basic functionality test failed: {e}")
        return False


async def test_agent_functionality():
    """Test enhanced agent functionality."""
    logger.info("Testing agent functionality")
    
    try:
        async with EnhancedBrowserSession() as browser_session:
            # Create agent with settings
            settings = AgentSettings(
                use_vision=True,
                max_failures=2,
                max_actions_per_step=3,
                use_thinking=True
            )
            
            async with EnhancedAgent(
                task="Test agent functionality",
                browser_session=browser_session,
                settings=settings
            ) as agent:
                
                logger.info(f"Agent created: {agent.state.agent_id}")
                
                # Test simple task
                task = "Navigate to https://httpbin.org/html and take a screenshot"
                result = await agent.run(task, max_steps=5)
                
                logger.info(f"Task result: {result.get('success', False)}")
                
                # Get agent stats
                stats = agent.get_stats()
                logger.info(f"Agent stats: {stats}")
                
                # Get task history
                history = agent.get_task_history()
                logger.info(f"Task history: {len(history)} tasks")
                
                return result.get('success', False)
    
    except Exception as e:
        logger.error(f"Agent functionality test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling and recovery."""
    logger.info("Testing error handling")
    
    try:
        async with EnhancedBrowserSession() as browser_session:
            settings = AgentSettings(
                max_failures=2,
                max_actions_per_step=2
            )
            
            async with EnhancedAgent(
                browser_session=browser_session,
                settings=settings
            ) as agent:
                
                # Test with invalid URL to trigger error handling
                task = "Navigate to https://invalid-url-that-does-not-exist.com"
                result = await agent.run(task, max_steps=3)
                
                # Should handle error gracefully
                logger.info(f"Error handling test: {'PASS' if not result.get('success') else 'FAIL'}")
                
                # Check that error was recorded
                stats = agent.get_stats()
                failed_tasks = stats.get('tasks_failed', 0)
                logger.info(f"Failed tasks recorded: {failed_tasks}")
                
                return failed_tasks > 0
    
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


async def test_performance():
    """Test system performance."""
    logger.info("Testing performance")
    
    try:
        import time
        
        start_time = time.time()
        
        # Test multiple concurrent operations
        tasks = []
        
        for i in range(3):
            async def test_concurrent_session(session_id):
                async with EnhancedBrowserSession(session_id=f"test_{session_id}") as session:
                    await session.navigate_to("https://httpbin.org/html")
                    await asyncio.sleep(1)  # Simulate work
                    return session.get_stats()
            
            tasks.append(test_concurrent_session(i))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        successful_sessions = sum(1 for r in results if not isinstance(r, Exception))
        
        logger.info(f"Performance test completed in {execution_time:.2f}s")
        logger.info(f"Successful concurrent sessions: {successful_sessions}/3")
        
        return successful_sessions >= 2  # Allow for some failures
        
    except Exception as e:
        logger.error(f"Performance test failed: {e}")
        return False


async def run_all_tests():
    """Run all tests and report results."""
    logger.info("Starting comprehensive system tests")
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Agent Functionality", test_agent_functionality),
        ("Error Handling", test_error_handling),
        ("Performance", test_performance)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results[test_name] = result
            status = "PASS" if result else "FAIL"
            logger.info(f"{test_name}: {status}")
            
        except Exception as e:
            logger.error(f"{test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    # Event bus stats
    event_stats = default_event_bus.get_stats()
    logger.info(f"Event bus stats: {event_stats}")
    
    return passed == total


async def main():
    """Main test function."""
    try:
        success = await run_all_tests()
        
        if success:
            logger.info("\n🎉 All tests passed! System is ready for production use.")
            sys.exit(0)
        else:
            logger.error("\n❌ Some tests failed. Please check the logs.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
